1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bearmod.loader"
4    android:versionCode="3"
5    android:versionName="1.3" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- Internet permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Storage permissions -->
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:10:5-80
16-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:10:22-77
17    <uses-permission
17-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:11:5-12:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:11:22-78
19        android:maxSdkVersion="28" />
19-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:12:9-35
20
21    <!-- For Android 10+ storage access -->
22    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
22-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:15:5-16:40
22-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:15:22-79
23
24    <!-- Vibration permission -->
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:19:5-66
25-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:19:22-63
26    <uses-permission android:name="android.permission.WAKE_LOCK" />
26-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
26-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
27    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
27-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
27-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
28-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
28-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
29
30    <permission
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
31        android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
35
36    <application
36-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:21:5-77:19
37        android:name="com.bearmod.loader.BearLoaderApplication"
37-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:22:9-46
38        android:allowBackup="true"
38-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:23:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
40        android:dataExtractionRules="@xml/data_extraction_rules"
40-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:24:9-65
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:fullBackupContent="@xml/backup_rules"
43-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:25:9-54
44        android:icon="@mipmap/ic_launcher"
44-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:26:9-43
45        android:label="@string/app_name"
45-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:27:9-41
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:28:9-54
47        android:supportsRtl="true"
47-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:29:9-35
48        android:theme="@style/Theme.BearLoader"
48-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:30:9-48
49        android:usesCleartextTraffic="true" >
49-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:31:9-44
50
51        <!-- Splash Activity -->
52        <activity
52-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:34:9-43:20
53            android:name="com.bearmod.loader.ui.splash.SplashActivity"
53-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:35:13-53
54            android:exported="true"
54-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:36:13-36
55            android:theme="@style/Theme.BearLoader.Splash"
55-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:37:13-59
56            android:windowSoftInputMode="adjustNothing" >
56-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:38:13-56
57            <intent-filter>
57-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:39:13-42:29
58                <action android:name="android.intent.action.MAIN" />
58-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:40:17-69
58-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:40:25-66
59
60                <category android:name="android.intent.category.LAUNCHER" />
60-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:41:17-77
60-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:41:27-74
61            </intent-filter>
62        </activity>
63
64        <!-- Login Activity -->
65        <activity
65-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:46:9-49:67
66            android:name="com.bearmod.loader.ui.auth.LoginActivity"
66-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:47:13-50
67            android:exported="false"
67-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:48:13-37
68            android:theme="@style/Theme.BearLoader.NoActionBar" />
68-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:49:13-64
69
70        <!-- Main Activity (Dashboard) -->
71        <activity
71-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:52:9-55:67
72            android:name="com.bearmod.loader.ui.main.MainActivity"
72-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:53:13-49
73            android:exported="false"
73-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:54:13-37
74            android:theme="@style/Theme.BearLoader.NoActionBar" />
74-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:55:13-64
75
76        <!-- Patch Execution Activity -->
77        <activity
77-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:58:9-61:67
78            android:name="com.bearmod.loader.ui.patch.PatchExecutionActivity"
78-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:59:13-60
79            android:exported="false"
79-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:60:13-37
80            android:theme="@style/Theme.BearLoader.NoActionBar" />
80-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:61:13-64
81
82        <!-- Download Activity -->
83        <activity
83-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:64:9-67:67
84            android:name="com.bearmod.loader.ui.download.DownloadActivity"
84-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:65:13-57
85            android:exported="false"
85-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:66:13-37
86            android:theme="@style/Theme.BearLoader.NoActionBar" />
86-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:67:13-64
87
88        <!-- Settings Activity -->
89        <activity
89-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:70:9-73:67
90            android:name="com.bearmod.loader.ui.settings.SettingsActivity"
90-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:71:13-57
91            android:exported="false"
91-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:72:13-37
92            android:theme="@style/Theme.BearLoader.NoActionBar" />
92-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:73:13-64
93
94        <provider
94-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
95            android:name="androidx.startup.InitializationProvider"
95-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
96            android:authorities="com.bearmod.loader.androidx-startup"
96-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
97            android:exported="false" >
97-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
98            <meta-data
98-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
99                android:name="androidx.work.WorkManagerInitializer"
99-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
100                android:value="androidx.startup" />
100-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
101            <meta-data
101-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.emoji2.text.EmojiCompatInitializer"
102-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
103                android:value="androidx.startup" />
103-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
105-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
106                android:value="androidx.startup" />
106-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
107            <meta-data
107-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
108-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
109                android:value="androidx.startup" />
109-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
110        </provider>
111
112        <service
112-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
113            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
113-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
114            android:directBootAware="false"
114-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
115            android:enabled="@bool/enable_system_alarm_service_default"
115-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
116            android:exported="false" />
116-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
117        <service
117-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
118            android:name="androidx.work.impl.background.systemjob.SystemJobService"
118-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
119            android:directBootAware="false"
119-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
120            android:enabled="@bool/enable_system_job_service_default"
120-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
121            android:exported="true"
121-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
122            android:permission="android.permission.BIND_JOB_SERVICE" />
122-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
123        <service
123-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
124            android:name="androidx.work.impl.foreground.SystemForegroundService"
124-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
125            android:directBootAware="false"
125-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
126            android:enabled="@bool/enable_system_foreground_service_default"
126-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
127            android:exported="false" />
127-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
128
129        <receiver
129-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
130            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
130-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
131            android:directBootAware="false"
131-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
132            android:enabled="true"
132-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
133            android:exported="false" />
133-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
134        <receiver
134-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
135            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
135-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
136            android:directBootAware="false"
136-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
137            android:enabled="false"
137-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
138            android:exported="false" >
138-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
139            <intent-filter>
139-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
140                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
140-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
140-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
141                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
141-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
141-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
142            </intent-filter>
143        </receiver>
144        <receiver
144-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
145            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
145-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
146            android:directBootAware="false"
146-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
147            android:enabled="false"
147-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
148            android:exported="false" >
148-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
149            <intent-filter>
149-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
150                <action android:name="android.intent.action.BATTERY_OKAY" />
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
151                <action android:name="android.intent.action.BATTERY_LOW" />
151-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
151-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
152            </intent-filter>
153        </receiver>
154        <receiver
154-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
155            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
155-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
156            android:directBootAware="false"
156-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
157            android:enabled="false"
157-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
158            android:exported="false" >
158-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
159            <intent-filter>
159-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
160                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
161                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
161-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
161-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
162            </intent-filter>
163        </receiver>
164        <receiver
164-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
165            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
165-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
166            android:directBootAware="false"
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
167            android:enabled="false"
167-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
168            android:exported="false" >
168-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
169            <intent-filter>
169-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
170                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
171            </intent-filter>
172        </receiver>
173        <receiver
173-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
174            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
174-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
175            android:directBootAware="false"
175-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
176            android:enabled="false"
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
177            android:exported="false" >
177-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
178            <intent-filter>
178-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
179                <action android:name="android.intent.action.BOOT_COMPLETED" />
179-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
179-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
180                <action android:name="android.intent.action.TIME_SET" />
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
181                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
181-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
181-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
182            </intent-filter>
183        </receiver>
184        <receiver
184-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
185            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
185-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
186            android:directBootAware="false"
186-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
187            android:enabled="@bool/enable_system_alarm_service_default"
187-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
188            android:exported="false" >
188-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
189            <intent-filter>
189-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
190                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
190-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
190-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
191            </intent-filter>
192        </receiver>
193        <receiver
193-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
194            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
194-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
195            android:directBootAware="false"
195-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
196            android:enabled="true"
196-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
197            android:exported="true"
197-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
198            android:permission="android.permission.DUMP" >
198-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
199            <intent-filter>
199-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
200                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
200-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
200-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
201            </intent-filter>
202        </receiver>
203
204        <uses-library
204-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
205            android:name="androidx.window.extensions"
205-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
206            android:required="false" />
206-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
207        <uses-library
207-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
208            android:name="androidx.window.sidecar"
208-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
209            android:required="false" />
209-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
210
211        <service
211-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
212            android:name="androidx.room.MultiInstanceInvalidationService"
212-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
213            android:directBootAware="true"
213-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
214            android:exported="false" />
214-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
215
216        <receiver
216-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
217            android:name="androidx.profileinstaller.ProfileInstallReceiver"
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
218            android:directBootAware="false"
218-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
219            android:enabled="true"
219-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
220            android:exported="true"
220-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
221            android:permission="android.permission.DUMP" >
221-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
222            <intent-filter>
222-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
223                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
223-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
223-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
224            </intent-filter>
225            <intent-filter>
225-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
226                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
226-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
226-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
227            </intent-filter>
228            <intent-filter>
228-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
229                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
230            </intent-filter>
231            <intent-filter>
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
232                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
233            </intent-filter>
234        </receiver>
235    </application>
236
237</manifest>
