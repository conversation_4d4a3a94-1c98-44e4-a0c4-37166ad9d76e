1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bearmod.loader"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- Internet permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Storage permissions -->
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:10:5-80
16-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:10:22-77
17    <uses-permission
17-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:11:5-12:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:11:22-78
19        android:maxSdkVersion="28" />
19-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:12:9-35
20
21    <!-- For Android 10+ storage access -->
22    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
22-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:15:5-16:40
22-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:15:22-79
23
24    <!-- Vibration permission -->
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:19:5-66
25-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:19:22-63
26    <uses-permission android:name="android.permission.WAKE_LOCK" />
26-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
26-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
27    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
27-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
27-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
28-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
28-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
29
30    <permission
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8f35fafa98a4d8ec2806f26be612e8\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
31        android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8f35fafa98a4d8ec2806f26be612e8\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8f35fafa98a4d8ec2806f26be612e8\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8f35fafa98a4d8ec2806f26be612e8\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8f35fafa98a4d8ec2806f26be612e8\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
35
36    <application
36-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:21:5-75:19
37        android:name="com.bearmod.loader.BearLoaderApplication"
37-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:22:9-46
38        android:allowBackup="true"
38-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:23:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8f35fafa98a4d8ec2806f26be612e8\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
40        android:dataExtractionRules="@xml/data_extraction_rules"
40-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:24:9-65
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:fullBackupContent="@xml/backup_rules"
43-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:25:9-54
44        android:icon="@mipmap/ic_launcher"
44-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:26:9-43
45        android:label="@string/app_name"
45-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:27:9-41
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:28:9-54
47        android:supportsRtl="true"
47-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:29:9-35
48        android:theme="@style/Theme.BearLoader"
48-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:30:9-48
49        android:usesCleartextTraffic="true" >
49-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:31:9-44
50
51        <!-- Splash Activity -->
52        <activity
52-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:34:9-42:20
53            android:name="com.bearmod.loader.ui.splash.SplashActivity"
53-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:35:13-53
54            android:exported="true"
54-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:36:13-36
55            android:theme="@style/Theme.BearLoader.Splash" >
55-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:37:13-59
56            <intent-filter>
56-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:38:13-41:29
57                <action android:name="android.intent.action.MAIN" />
57-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:39:17-69
57-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:39:25-66
58
59                <category android:name="android.intent.category.LAUNCHER" />
59-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:40:17-77
59-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:40:27-74
60            </intent-filter>
61        </activity>
62
63        <!-- Login Activity -->
64        <activity
64-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:45:9-48:67
65            android:name="com.bearmod.loader.ui.auth.LoginActivity"
65-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:46:13-50
66            android:exported="false"
66-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:47:13-37
67            android:theme="@style/Theme.BearLoader.NoActionBar" />
67-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:48:13-64
68
69        <!-- Main Activity (Dashboard) -->
70        <activity
70-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:51:9-54:67
71            android:name="com.bearmod.loader.ui.main.MainActivity"
71-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:52:13-49
72            android:exported="false"
72-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:53:13-37
73            android:theme="@style/Theme.BearLoader.NoActionBar" />
73-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:54:13-64
74
75        <!-- Patch Execution Activity -->
76        <activity
76-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:57:9-60:67
77            android:name="com.bearmod.loader.ui.patch.PatchExecutionActivity"
77-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:58:13-60
78            android:exported="false"
78-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:59:13-37
79            android:theme="@style/Theme.BearLoader.NoActionBar" />
79-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:60:13-64
80
81        <!-- Download Activity -->
82        <activity
82-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:63:9-66:67
83            android:name="com.bearmod.loader.ui.download.DownloadActivity"
83-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:64:13-57
84            android:exported="false"
84-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:65:13-37
85            android:theme="@style/Theme.BearLoader.NoActionBar" />
85-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:66:13-64
86
87        <!-- Settings Activity -->
88        <activity
88-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:69:9-72:67
89            android:name="com.bearmod.loader.ui.settings.SettingsActivity"
89-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:70:13-57
90            android:exported="false"
90-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:71:13-37
91            android:theme="@style/Theme.BearLoader.NoActionBar" />
91-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:72:13-64
92
93        <provider
93-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
94            android:name="androidx.startup.InitializationProvider"
94-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
95            android:authorities="com.bearmod.loader.androidx-startup"
95-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
96            android:exported="false" >
96-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
97            <meta-data
97-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
98                android:name="androidx.work.WorkManagerInitializer"
98-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
99                android:value="androidx.startup" />
99-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
100            <meta-data
100-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3a68666eaa20082bf0a73a98b9dd300\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
101                android:name="androidx.emoji2.text.EmojiCompatInitializer"
101-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3a68666eaa20082bf0a73a98b9dd300\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
102                android:value="androidx.startup" />
102-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3a68666eaa20082bf0a73a98b9dd300\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
103            <meta-data
103-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da03fbda249b303caad8103dc25276b6\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
104                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
104-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da03fbda249b303caad8103dc25276b6\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
105                android:value="androidx.startup" />
105-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da03fbda249b303caad8103dc25276b6\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
106            <meta-data
106-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
107                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
107-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
108                android:value="androidx.startup" />
108-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
109        </provider>
110
111        <service
111-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
112            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
112-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
113            android:directBootAware="false"
113-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
114            android:enabled="@bool/enable_system_alarm_service_default"
114-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
115            android:exported="false" />
115-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
116        <service
116-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
117            android:name="androidx.work.impl.background.systemjob.SystemJobService"
117-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
118            android:directBootAware="false"
118-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
119            android:enabled="@bool/enable_system_job_service_default"
119-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
120            android:exported="true"
120-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
121            android:permission="android.permission.BIND_JOB_SERVICE" />
121-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
122        <service
122-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
123            android:name="androidx.work.impl.foreground.SystemForegroundService"
123-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
124            android:directBootAware="false"
124-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
125            android:enabled="@bool/enable_system_foreground_service_default"
125-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
126            android:exported="false" />
126-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
127
128        <receiver
128-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
129            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
129-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
130            android:directBootAware="false"
130-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
131            android:enabled="true"
131-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
132            android:exported="false" />
132-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
133        <receiver
133-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
134            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
134-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
135            android:directBootAware="false"
135-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
136            android:enabled="false"
136-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
137            android:exported="false" >
137-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
138            <intent-filter>
138-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
139                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
139-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
139-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
140                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
140-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
140-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
141            </intent-filter>
142        </receiver>
143        <receiver
143-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
144            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
144-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
145            android:directBootAware="false"
145-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
146            android:enabled="false"
146-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
147            android:exported="false" >
147-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
148            <intent-filter>
148-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
149                <action android:name="android.intent.action.BATTERY_OKAY" />
149-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
149-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
150                <action android:name="android.intent.action.BATTERY_LOW" />
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
151            </intent-filter>
152        </receiver>
153        <receiver
153-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
154            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
154-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
155            android:directBootAware="false"
155-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
156            android:enabled="false"
156-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
157            android:exported="false" >
157-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
158            <intent-filter>
158-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
159                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
159-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
159-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
160                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
161            </intent-filter>
162        </receiver>
163        <receiver
163-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
164            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
164-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
165            android:directBootAware="false"
165-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
166            android:enabled="false"
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
167            android:exported="false" >
167-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
168            <intent-filter>
168-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
169                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
169-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
169-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
170            </intent-filter>
171        </receiver>
172        <receiver
172-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
173            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
173-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
174            android:directBootAware="false"
174-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
175            android:enabled="false"
175-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
176            android:exported="false" >
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
177            <intent-filter>
177-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
178                <action android:name="android.intent.action.BOOT_COMPLETED" />
178-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
178-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
179                <action android:name="android.intent.action.TIME_SET" />
179-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
179-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
180                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
181            </intent-filter>
182        </receiver>
183        <receiver
183-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
184            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
184-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
185            android:directBootAware="false"
185-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
186            android:enabled="@bool/enable_system_alarm_service_default"
186-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
187            android:exported="false" >
187-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
188            <intent-filter>
188-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
189                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
189-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
189-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
190            </intent-filter>
191        </receiver>
192        <receiver
192-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
193            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
193-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
194            android:directBootAware="false"
194-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
195            android:enabled="true"
195-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
196            android:exported="true"
196-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
197            android:permission="android.permission.DUMP" >
197-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
198            <intent-filter>
198-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
199                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
199-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
199-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb4d9a73aed0d8834022c831ce0e9b15\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
200            </intent-filter>
201        </receiver>
202
203        <uses-library
203-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f598f0b6582248cb4c6cfbf3e015a5\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
204            android:name="androidx.window.extensions"
204-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f598f0b6582248cb4c6cfbf3e015a5\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
205            android:required="false" />
205-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f598f0b6582248cb4c6cfbf3e015a5\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
206        <uses-library
206-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f598f0b6582248cb4c6cfbf3e015a5\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
207            android:name="androidx.window.sidecar"
207-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f598f0b6582248cb4c6cfbf3e015a5\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
208            android:required="false" />
208-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f598f0b6582248cb4c6cfbf3e015a5\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
209
210        <service
210-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6dc9b9b27566c531c16849e9636df1f\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
211            android:name="androidx.room.MultiInstanceInvalidationService"
211-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6dc9b9b27566c531c16849e9636df1f\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
212            android:directBootAware="true"
212-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6dc9b9b27566c531c16849e9636df1f\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
213            android:exported="false" />
213-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6dc9b9b27566c531c16849e9636df1f\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
214
215        <receiver
215-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
216            android:name="androidx.profileinstaller.ProfileInstallReceiver"
216-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
217            android:directBootAware="false"
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
218            android:enabled="true"
218-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
219            android:exported="true"
219-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
220            android:permission="android.permission.DUMP" >
220-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
221            <intent-filter>
221-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
222                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
222-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
222-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
223            </intent-filter>
224            <intent-filter>
224-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
225                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
225-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
225-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
226            </intent-filter>
227            <intent-filter>
227-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
228                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
228-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
228-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
229            </intent-filter>
230            <intent-filter>
230-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
231                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb7ca9cd07f3ac11767cd369a35e0507\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
232            </intent-filter>
233        </receiver>
234    </application>
235
236</manifest>
