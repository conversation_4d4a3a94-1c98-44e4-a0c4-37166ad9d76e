1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bearmod.loader"
4    android:versionCode="1"
5    android:versionName="1.3" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- Internet permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Storage permissions -->
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:10:5-80
16-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:10:22-77
17    <uses-permission
17-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:11:5-12:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:11:22-78
19        android:maxSdkVersion="28" />
19-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:12:9-35
20
21    <!-- For Android 10+ storage access -->
22    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
22-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:15:5-16:40
22-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:15:22-79
23
24    <!-- Vibration permission -->
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:19:5-66
25-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:19:22-63
26    <uses-permission android:name="android.permission.WAKE_LOCK" />
26-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
26-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
27    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
27-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
27-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
28-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
28-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
29
30    <permission
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
31        android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
35
36    <application
36-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:21:5-86:19
37        android:name="com.bearmod.loader.BearLoaderApplication"
37-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:22:9-46
38        android:allowBackup="true"
38-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:23:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
40        android:dataExtractionRules="@xml/data_extraction_rules"
40-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:24:9-65
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:fullBackupContent="@xml/backup_rules"
43-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:25:9-54
44        android:icon="@mipmap/ic_launcher"
44-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:26:9-43
45        android:label="@string/app_name"
45-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:27:9-41
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:28:9-54
47        android:supportsRtl="true"
47-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:29:9-35
48        android:theme="@style/Theme.BearLoader"
48-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:30:9-48
49        android:usesCleartextTraffic="true" >
49-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:31:9-44
50
51        <!-- Splash Activity -->
52        <activity
52-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:34:9-42:20
53            android:name="com.bearmod.loader.ui.splash.SplashActivity"
53-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:35:13-53
54            android:exported="true"
54-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:36:13-36
55            android:theme="@style/Theme.BearLoader.Splash" >
55-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:37:13-59
56            <intent-filter>
56-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:38:13-41:29
57                <action android:name="android.intent.action.MAIN" />
57-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:39:17-69
57-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:39:25-66
58
59                <category android:name="android.intent.category.LAUNCHER" />
59-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:40:17-77
59-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:40:27-74
60            </intent-filter>
61        </activity>
62
63        <!-- Login Activity -->
64        <activity
64-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:45:9-48:67
65            android:name="com.bearmod.loader.ui.auth.LoginActivity"
65-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:46:13-50
66            android:exported="false"
66-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:47:13-37
67            android:theme="@style/Theme.BearLoader.NoActionBar" />
67-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:48:13-64
68
69        <!-- Main Activity (Dashboard) -->
70        <activity
70-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:51:9-54:67
71            android:name="com.bearmod.loader.ui.main.MainActivity"
71-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:52:13-49
72            android:exported="false"
72-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:53:13-37
73            android:theme="@style/Theme.BearLoader.NoActionBar" />
73-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:54:13-64
74
75        <!-- Patch Execution Activity -->
76        <activity
76-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:57:9-60:67
77            android:name="com.bearmod.loader.ui.patch.PatchExecutionActivity"
77-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:58:13-60
78            android:exported="false"
78-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:59:13-37
79            android:theme="@style/Theme.BearLoader.NoActionBar" />
79-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:60:13-64
80
81        <!-- Download Activity -->
82        <activity
82-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:63:9-66:67
83            android:name="com.bearmod.loader.ui.download.DownloadActivity"
83-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:64:13-57
84            android:exported="false"
84-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:65:13-37
85            android:theme="@style/Theme.BearLoader.NoActionBar" />
85-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:66:13-64
86
87        <!-- Settings Activity -->
88        <activity
88-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:69:9-72:67
89            android:name="com.bearmod.loader.ui.settings.SettingsActivity"
89-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:70:13-57
90            android:exported="false"
90-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:71:13-37
91            android:theme="@style/Theme.BearLoader.NoActionBar" />
91-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:72:13-64
92
93        <!-- KeyAuth Test Activity (Debug only) -->
94        <activity
94-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:75:9-84:20
95            android:name="com.bearmod.loader.test.KeyAuthTestActivity"
95-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:76:13-53
96            android:exported="true"
96-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:77:13-36
97            android:theme="@style/Theme.BearLoader.NoActionBar" >
97-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:78:13-64
98            <intent-filter>
98-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:79:13-83:29
99                <action android:name="android.intent.action.VIEW" />
99-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:80:17-69
99-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:80:25-66
100
101                <category android:name="android.intent.category.DEFAULT" />
101-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:81:17-76
101-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:81:27-73
102
103                <data
103-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:82:17-70
104                    android:host="test"
104-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:82:48-67
105                    android:scheme="keyauth" />
105-->D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:82:23-47
106            </intent-filter>
107        </activity>
108
109        <provider
109-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
110            android:name="androidx.startup.InitializationProvider"
110-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
111            android:authorities="com.bearmod.loader.androidx-startup"
111-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
112            android:exported="false" >
112-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
113            <meta-data
113-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
114                android:name="androidx.work.WorkManagerInitializer"
114-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
115                android:value="androidx.startup" />
115-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
116            <meta-data
116-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
117                android:name="androidx.emoji2.text.EmojiCompatInitializer"
117-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
118                android:value="androidx.startup" />
118-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
119            <meta-data
119-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
120                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
120-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
121                android:value="androidx.startup" />
121-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
122            <meta-data
122-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
123                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
123-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
124                android:value="androidx.startup" />
124-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
125        </provider>
126
127        <service
127-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
128            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
128-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
129            android:directBootAware="false"
129-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
130            android:enabled="@bool/enable_system_alarm_service_default"
130-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
131            android:exported="false" />
131-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
132        <service
132-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
133            android:name="androidx.work.impl.background.systemjob.SystemJobService"
133-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
134            android:directBootAware="false"
134-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
135            android:enabled="@bool/enable_system_job_service_default"
135-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
136            android:exported="true"
136-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
137            android:permission="android.permission.BIND_JOB_SERVICE" />
137-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
138        <service
138-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
139            android:name="androidx.work.impl.foreground.SystemForegroundService"
139-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
140            android:directBootAware="false"
140-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
141            android:enabled="@bool/enable_system_foreground_service_default"
141-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
142            android:exported="false" />
142-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
143
144        <receiver
144-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
145            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
145-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
146            android:directBootAware="false"
146-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
147            android:enabled="true"
147-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
148            android:exported="false" />
148-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
149        <receiver
149-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
150            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
151            android:directBootAware="false"
151-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
152            android:enabled="false"
152-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
153            android:exported="false" >
153-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
154            <intent-filter>
154-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
155                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
155-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
155-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
156                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
156-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
156-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
157            </intent-filter>
158        </receiver>
159        <receiver
159-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
160            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
161            android:directBootAware="false"
161-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
162            android:enabled="false"
162-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
163            android:exported="false" >
163-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
164            <intent-filter>
164-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
165                <action android:name="android.intent.action.BATTERY_OKAY" />
165-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
165-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
166                <action android:name="android.intent.action.BATTERY_LOW" />
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
167            </intent-filter>
168        </receiver>
169        <receiver
169-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
170            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
171            android:directBootAware="false"
171-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
172            android:enabled="false"
172-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
173            android:exported="false" >
173-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
174            <intent-filter>
174-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
175                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
175-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
175-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
176                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
177            </intent-filter>
178        </receiver>
179        <receiver
179-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
180            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
181            android:directBootAware="false"
181-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
182            android:enabled="false"
182-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
183            android:exported="false" >
183-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
184            <intent-filter>
184-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
185                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
185-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
185-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
186            </intent-filter>
187        </receiver>
188        <receiver
188-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
189            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
189-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
190            android:directBootAware="false"
190-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
191            android:enabled="false"
191-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
192            android:exported="false" >
192-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
193            <intent-filter>
193-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
194                <action android:name="android.intent.action.BOOT_COMPLETED" />
194-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
194-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
195                <action android:name="android.intent.action.TIME_SET" />
195-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
195-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
196                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
196-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
196-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
197            </intent-filter>
198        </receiver>
199        <receiver
199-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
200            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
200-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
201            android:directBootAware="false"
201-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
202            android:enabled="@bool/enable_system_alarm_service_default"
202-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
203            android:exported="false" >
203-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
204            <intent-filter>
204-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
205                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
205-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
205-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
206            </intent-filter>
207        </receiver>
208        <receiver
208-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
209            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
209-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
210            android:directBootAware="false"
210-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
211            android:enabled="true"
211-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
212            android:exported="true"
212-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
213            android:permission="android.permission.DUMP" >
213-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
214            <intent-filter>
214-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
215                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
215-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
215-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
216            </intent-filter>
217        </receiver>
218
219        <uses-library
219-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
220            android:name="androidx.window.extensions"
220-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
221            android:required="false" />
221-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
222        <uses-library
222-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
223            android:name="androidx.window.sidecar"
223-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
224            android:required="false" />
224-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
225
226        <service
226-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
227            android:name="androidx.room.MultiInstanceInvalidationService"
227-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
228            android:directBootAware="true"
228-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
229            android:exported="false" />
229-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
230
231        <receiver
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
232            android:name="androidx.profileinstaller.ProfileInstallReceiver"
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
233            android:directBootAware="false"
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
234            android:enabled="true"
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
235            android:exported="true"
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
236            android:permission="android.permission.DUMP" >
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
237            <intent-filter>
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
238                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
239            </intent-filter>
240            <intent-filter>
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
241                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
242            </intent-filter>
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
244                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
245            </intent-filter>
246            <intent-filter>
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
247                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
248            </intent-filter>
249        </receiver>
250    </application>
251
252</manifest>
