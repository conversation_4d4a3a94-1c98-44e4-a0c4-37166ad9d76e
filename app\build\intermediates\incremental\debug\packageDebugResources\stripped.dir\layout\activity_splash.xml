<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.splash.SplashActivity">

    <!-- Video Background for Bear Animation -->
    <VideoView
        android:id="@+id/video_splash"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_centerInParent="true"
        android:background="@color/background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Overlay Container for Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/overlay_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/background_overlay"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- Fallback Logo (shown if video fails to load) -->
        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:alpha="0.0"
            android:contentDescription="@string/app_logo"
            android:src="@drawable/logo_splash"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4" />

        <!-- App Name with Modern Typography -->
        <TextView
            android:id="@+id/tv_app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="120dp"
            android:alpha="0.0"
            android:text="@string/app_name"
            android:textAppearance="@style/TextAppearance.Material3.HeadlineLarge"
            android:textColor="@color/text_primary"
            android:textSize="32sp"
            android:textStyle="bold"
            android:shadowColor="@color/background"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- App Version -->
        <TextView
            android:id="@+id/tv_app_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:alpha="0.0"
            android:text="@string/app_version"
            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
            android:textColor="@color/text_secondary"
            android:textSize="18sp"
            android:shadowColor="@color/background"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_app_name" />

        <!-- Modern Progress Indicator -->
        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:alpha="0.0"
            android:indeterminate="true"
            app:indicatorColor="@color/primary"
            app:indicatorSize="48dp"
            app:trackColor="@color/surface_container_high"
            app:trackThickness="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_app_version" />

        <!-- Loading Text -->
        <TextView
            android:id="@+id/tv_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:alpha="0.0"
            android:text="@string/loading"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progress_bar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Video Loading Overlay -->
    <View
        android:id="@+id/video_loading_overlay"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/background"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
