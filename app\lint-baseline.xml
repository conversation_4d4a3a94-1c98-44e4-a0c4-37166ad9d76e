<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.10.0" type="baseline" client="gradle" dependencies="false" name="AGP (8.10.0)" variant="all" version="8.10.0">

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            String eta = String.format(&quot;%d:%02d&quot;, etaMinutes, etaSeconds);"
        errorLine2="                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java"
            line="165"
            column="26"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            String fileName = release.getName().replaceAll(&quot;\\s+&quot;, &quot;-&quot;).toLowerCase() + &quot;.zip&quot;;"
        errorLine2="                                                                        ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/download/DownloadManager.java"
            line="154"
            column="73"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        String cleanKey = licenseKey.replace(&quot;-&quot;, &quot;&quot;).replace(&quot; &quot;, &quot;&quot;).toUpperCase();"
        errorLine2="                                                                       ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/auth/LoginActivity.java"
            line="161"
            column="72"/>
    </issue>

    <issue
        id="CustomSplashScreen"
        message="The application should not provide its own launch screen"
        errorLine1="public class SplashActivity extends AppCompatActivity {"
        errorLine2="             ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/splash/SplashActivity.java"
            line="28"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.concurrent:concurrent-futures than 1.1.0 is available: 1.2.0"
        errorLine1="    implementation(&quot;androidx.concurrent:concurrent-futures:1.1.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="123"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.guava:guava than 30.1-android is available: 31.1-android"
        errorLine1="    implementation(&quot;com.google.guava:guava:30.1-android&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="124"
            column="20"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="            android:tint=&quot;@color/primary&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_release.xml"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="PluralsCandidate"
        message="Formatting %d followed by words (&quot;days&quot;): This should probably be a plural rather than a string"
        errorLine1="    &lt;string name=&quot;days_remaining&quot;>%1$d days remaining&lt;/string>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="18"
            column="5"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        message="`checkClientTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.KeyAuth-Archive/KeyAuth-JAVA-api/7adb05794e/afd556a5ef6873ceb20992c4c87f155189463865/KeyAuth-JAVA-api-7adb05794e.jar"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        message="`checkServerTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.KeyAuth-Archive/KeyAuth-JAVA-api/7adb05794e/afd556a5ef6873ceb20992c4c87f155189463865/KeyAuth-JAVA-api-7adb05794e.jar"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        adapter.notifyDataSetChanged();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/test/ApiTestActivity.java"
            line="71"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `SDK_INT` is never &lt; 24"
        errorLine1="        if (Build.VERSION.SDK_INT &lt; MIN_ANDROID_VERSION) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/util/BuildValidator.java"
            line="48"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `CloudSyncManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static CloudSyncManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/cloud/CloudSyncManager.java"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `DockerManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static DockerManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/patch/DockerManager.java"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `DownloadManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static DownloadManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/download/DownloadManager.java"
            line="34"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `FridaManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static FridaManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/patch/FridaManager.java"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `PatchManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static PatchManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/patch/PatchManager.java"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="        android:pathData=&quot;M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z&quot; />"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_settings.xml"
            line="9"
            column="27"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)"
        errorLine1="    android:background=&quot;@color/background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_download.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)"
        errorLine1="    android:background=&quot;@color/background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)"
        errorLine1="    android:background=&quot;@color/background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_patch_execution.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)"
        errorLine1="    android:background=&quot;@color/background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_settings.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/primary` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)"
        errorLine1="    android:background=&quot;@color/primary&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/nav_header.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.app_logo` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/app_logo.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_700&quot;>#FF018786&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_light&quot;>#4CAF50&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="21"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.margin_medium` appears to be unused"
        errorLine1="        &lt;dimen name=&quot;margin_medium&quot;>16dp&lt;/dimen>"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="2"
            column="16"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.margin_small` appears to be unused"
        errorLine1="        &lt;dimen name=&quot;margin_small&quot;>8dp&lt;/dimen>"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="3"
            column="16"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.text_size_large` appears to be unused"
        errorLine1="        &lt;dimen name=&quot;text_size_large&quot;>18sp&lt;/dimen>"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="4"
            column="16"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.text_size_medium` appears to be unused"
        errorLine1="        &lt;dimen name=&quot;text_size_medium&quot;>16sp&lt;/dimen>"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="5"
            column="16"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.text_size_small` appears to be unused"
        errorLine1="        &lt;dimen name=&quot;text_size_small&quot;>14sp&lt;/dimen>"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="6"
            column="16"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.text_size_caption` appears to be unused"
        errorLine1="        &lt;dimen name=&quot;text_size_caption&quot;>12sp&lt;/dimen>&lt;/resources>"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="7"
            column="16"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_launcher_background` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_launcher_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_launcher_foreground` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_launcher_foreground.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.app_version_static` appears to be unused"
        errorLine1="    &lt;string name=&quot;app_version_static&quot;>Version 1.0.0&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="4"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.select_target` appears to be unused"
        errorLine1="    &lt;string name=&quot;select_target&quot;>Select Target&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.patch_status` appears to be unused"
        errorLine1="    &lt;string name=&quot;patch_status&quot;>Patch Status&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.downloading` appears to be unused"
        errorLine1="    &lt;string name=&quot;downloading&quot;>Downloading… %1$s / %2$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.download_complete` appears to be unused"
        errorLine1="    &lt;string name=&quot;download_complete&quot;>Download Complete&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="63"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.notification_settings` appears to be unused"
        errorLine1="    &lt;string name=&quot;notification_settings&quot;>Notification Settings&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="78"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.dark_mode` appears to be unused"
        errorLine1="    &lt;string name=&quot;dark_mode&quot;>Dark Mode&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="80"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.language` appears to be unused"
        errorLine1="    &lt;string name=&quot;language&quot;>Language&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="81"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.set_custom_domain` appears to be unused"
        errorLine1="    &lt;string name=&quot;set_custom_domain&quot;>Set Custom Domain&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="94"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.retry` appears to be unused"
        errorLine1="    &lt;string name=&quot;retry&quot;>Retry&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="100"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.loading` appears to be unused"
        errorLine1="    &lt;string name=&quot;loading&quot;>Loading…&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="101"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.info` appears to be unused"
        errorLine1="    &lt;string name=&quot;info&quot;>Information&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="105"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_BearLoader_Dialog` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.BearLoader.Dialog&quot; parent=&quot;ThemeOverlay.MaterialComponents.Dialog.Alert&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="36"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_BearLoader_Headline6` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.BearLoader.Headline6&quot;"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="59"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_BearLoader_Body1` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.BearLoader.Body1&quot; parent=&quot;TextAppearance.MaterialComponents.Body1&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="66"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_BearLoader_Body2` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.BearLoader.Body2&quot; parent=&quot;TextAppearance.MaterialComponents.Body2&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="72"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_BearLoader_Caption` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.BearLoader.Caption&quot;"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="77"
            column="12"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/logo.png` in densityless folder">
        <location
            file="src/main/res/drawable/logo.png"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.concurrent:concurrent-futures:1.1.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="123"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.guava:guava:30.1-android&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="124"
            column="20"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/item_api_test_result.xml"
            line="9"
            column="6"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        binding.tvStatus.setText(&quot;Testing API connections...&quot;);"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/test/ApiTestActivity.java"
            line="75"
            column="34"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                    binding.tvStatus.setText(&quot;At least one API connection was successful&quot;);"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/test/ApiTestActivity.java"
            line="96"
            column="46"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                    binding.tvStatus.setText(&quot;All API connections failed&quot;);"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/test/ApiTestActivity.java"
            line="99"
            column="46"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        binding.tvStatus.setText(&quot;Testing license validation...&quot;);"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/test/ApiTestActivity.java"
            line="112"
            column="34"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            binding.tvStatus.setText(&quot;Please enter a license key&quot;);"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/test/ApiTestActivity.java"
            line="118"
            column="38"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                binding.tvStatus.setText(&quot;License validation successful: &quot; + result.getMessage());"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/test/ApiTestActivity.java"
            line="129"
            column="42"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvStatus.setText(&quot;License validation successful: &quot; + result.getMessage());"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/test/ApiTestActivity.java"
            line="129"
            column="42"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                binding.tvStatus.setText(&quot;License validation failed: &quot; + error);"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/test/ApiTestActivity.java"
            line="144"
            column="42"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvStatus.setText(&quot;License validation failed: &quot; + error);"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/test/ApiTestActivity.java"
            line="144"
            column="42"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            binding.tvDownloadPercentage.setText(progress + &quot;%&quot;);"
        errorLine2="                                                 ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java"
            line="179"
            column="50"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            tvCustomDomainLabel.setText(&quot;Custom Domain: (Active)&quot;);"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/auth/KeyAuthTestActivity.java"
            line="62"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            tvCustomDomainLabel.setText(&quot;Custom Domain:&quot;);"
        errorLine2="                                        ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/auth/KeyAuthTestActivity.java"
            line="65"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        tvResult.setText(&quot;Testing KeyAuth connection...&quot;);"
        errorLine2="                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/auth/KeyAuthTestActivity.java"
            line="97"
            column="26"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                tvResult.setText(&quot;✅ &quot; + message);"
        errorLine2="                                 ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/auth/KeyAuthTestActivity.java"
            line="107"
            column="34"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                tvResult.setText(&quot;❌ &quot; + message);"
        errorLine2="                                 ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/auth/KeyAuthTestActivity.java"
            line="109"
            column="34"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        binding.tvPrimaryUrl.setText(&quot;Primary URL: &quot; + DirectKeyAuthManager.getApiUrl());"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/license/LicenseTestActivity.java"
            line="62"
            column="38"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        binding.tvPrimaryUrl.setText(&quot;Primary URL: &quot; + DirectKeyAuthManager.getApiUrl());"
        errorLine2="                                     ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/license/LicenseTestActivity.java"
            line="62"
            column="38"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        binding.tvAlternateUrl.setText(&quot;Alternate URL: &quot; + DirectKeyAuthManager.getAlternateApiUrl());"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/license/LicenseTestActivity.java"
            line="65"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        binding.tvAlternateUrl.setText(&quot;Alternate URL: &quot; + DirectKeyAuthManager.getAlternateApiUrl());"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/license/LicenseTestActivity.java"
            line="65"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        binding.tvDeviceId.setText(&quot;Device ID: &quot; + HWID.getHWID());"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/license/LicenseTestActivity.java"
            line="68"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        binding.tvDeviceId.setText(&quot;Device ID: &quot; + HWID.getHWID());"
        errorLine2="                                   ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/license/LicenseTestActivity.java"
            line="68"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                binding.tvConnectionStatus.setText(&quot;Connection failed: &quot; + error);"
        errorLine2="                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/license/LicenseTestActivity.java"
            line="98"
            column="52"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvConnectionStatus.setText(&quot;Connection failed: &quot; + error);"
        errorLine2="                                                   ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/license/LicenseTestActivity.java"
            line="98"
            column="52"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                binding.tvLicenseStatus.setText(&quot;License is valid until &quot; + expiryDate);"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/license/LicenseTestActivity.java"
            line="137"
            column="49"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvLicenseStatus.setText(&quot;License is valid until &quot; + expiryDate);"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/license/LicenseTestActivity.java"
            line="137"
            column="49"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                binding.tvLicenseStatus.setText(&quot;License validation failed: &quot; + error);"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/license/LicenseTestActivity.java"
            line="148"
            column="49"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvLicenseStatus.setText(&quot;License validation failed: &quot; + error);"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/license/LicenseTestActivity.java"
            line="148"
            column="49"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Drawer Test Activity&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Drawer Test Activity&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_drawer_test.xml"
            line="35"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;This activity tests the navigation drawer functionality.&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;This activity tests the navigation drawer functionality.&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_drawer_test.xml"
            line="43"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Open Drawer&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Open Drawer&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_drawer_test.xml"
            line="51"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;KeyAuth API Connection Test&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;KeyAuth API Connection Test&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_keyauth_test.xml"
            line="15"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;This test will verify if the KeyAuth API domain is working correctly. It will attempt to connect to the API and validate a test license key.&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;This test will verify if the KeyAuth API domain is working correctly. It will attempt to connect to the API and validate a test license key.&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_keyauth_test.xml"
            line="27"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;API Domains:&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;API Domains:&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_keyauth_test.xml"
            line="38"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Primary:&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Primary:&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_keyauth_test.xml"
            line="48"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;https://keyauth.win/api/1.2/&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;https://keyauth.win/api/1.2/&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_keyauth_test.xml"
            line="57"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Alternate:&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Alternate:&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_keyauth_test.xml"
            line="68"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;https://prod.keyauth.com/api/1.2/&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;https://prod.keyauth.com/api/1.2/&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_keyauth_test.xml"
            line="77"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Custom Domain:&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Custom Domain:&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_keyauth_test.xml"
            line="88"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Set Custom Domain&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Set Custom Domain&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_keyauth_test.xml"
            line="116"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Test Connection&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Test Connection&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_keyauth_test.xml"
            line="126"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;License Server Connection&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;License Server Connection&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_license_test.xml"
            line="50"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Test connection to the license server&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;Test connection to the license server&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_license_test.xml"
            line="57"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Test Connection&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;Test Connection&quot; />"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_license_test.xml"
            line="65"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;License Validation&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;License Validation&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_license_test.xml"
            line="97"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Validate your license key&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;Validate your license key&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_license_test.xml"
            line="104"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;License Key&quot;, should use `@string` resource"
        errorLine1="                        android:hint=&quot;License Key&quot;>"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_license_test.xml"
            line="111"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Validate License&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;Validate License&quot; />"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_license_test.xml"
            line="126"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Server Information&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;Server Information&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_license_test.xml"
            line="157"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Current server configuration&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;Current server configuration&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_license_test.xml"
            line="164"
            column="25"/>
    </issue>

</issues>
