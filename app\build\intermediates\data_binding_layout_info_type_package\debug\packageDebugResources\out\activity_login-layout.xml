<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="190" endOffset="51"/></Target><Target id="@+id/iv_logo" view="ImageView"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="51"/></Target><Target id="@+id/tv_app_name" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="31" endOffset="60"/></Target><Target id="@+id/tv_login_subtitle" view="TextView"><Expressions/><location startLine="33" startOffset="4" endLine="43" endOffset="64"/></Target><Target id="@+id/card_login" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="45" startOffset="4" endLine="127" endOffset="55"/></Target><Target id="@+id/til_license_key" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="73" startOffset="12" endLine="100" endOffset="67"/></Target><Target id="@+id/et_license_key" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="89" startOffset="16" endLine="98" endOffset="78"/></Target><Target id="@+id/switch_remember" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="102" startOffset="12" endLine="110" endOffset="53"/></Target><Target id="@+id/btn_login" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="112" startOffset="12" endLine="123" endOffset="53"/></Target><Target id="@+id/tv_version" view="TextView"><Expressions/><location startLine="129" startOffset="4" endLine="139" endOffset="55"/></Target><Target id="@+id/progress_login" view="ProgressBar"><Expressions/><location startLine="142" startOffset="4" endLine="151" endOffset="51"/></Target><Target id="@+id/progress_initializing" view="ProgressBar"><Expressions/><location startLine="154" startOffset="4" endLine="162" endOffset="51"/></Target><Target id="@+id/success_animation" view="com.airbnb.lottie.LottieAnimationView"><Expressions/><location startLine="165" startOffset="4" endLine="176" endOffset="52"/></Target><Target id="@+id/view_overlay" view="View"><Expressions/><location startLine="179" startOffset="4" endLine="188" endOffset="51"/></Target></Targets></Layout>