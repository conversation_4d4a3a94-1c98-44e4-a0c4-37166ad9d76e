-- Merging decision tree log ---
manifest
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:2:1-79:12
MERGED from [androidx.databinding:viewbinding:8.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bb3a81c74d15c56a989d834df172a81\transformed\viewbinding-8.10.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97afcf84480752d7a418592ccdff1c40\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28f50267faca26958223bb8f925dbd83\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b622a2188e71c5a94ec3d3dca0d0346\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b19140129864dc831a67762e70345bb\transformed\navigation-ui-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a95a1075bc0403a1ca2712bac24841aa\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a44f930cd05df25e4d7fb3de1fecf309\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:lottie:6.6.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96d381843322b4f6d1a73e69fba6426c\transformed\lottie-6.6.6\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08eff8b9bceb77a4adcd616411db578c\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84e22dff9d3244eb5f30d696cec58c96\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\225f2e11b6c3a761c794807ea43fee3a\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\043eb9b4860c33e7bc5aeacc39177736\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3037431f73c5541471bc5a03fb28dc9\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment-ktx:1.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9af2d3f16075cce64beccbaa636857a7\transformed\fragment-ktx-1.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eed853c0261802e4d9e70faf27b55f23\transformed\recyclerview-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b706937324fd53a4b4ebf9d99bfa76c4\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03a3ec360ca4a6670ac4c7dddb9dd063\transformed\fragment-1.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff72eb6b37be78e33325a299cef88a9\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf0d58f032068de07cddaab312ce5c0a\transformed\activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ca7aab97726176c596478f9fd1a4002\transformed\activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057d5f21678a2f8e98dd0c28fac64b68\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4be8df471c2a3e7d594e90aba5476348\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c45357efb1170dcabe56a0015399a7a\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4650b5b43fed97c925c90064bab87ab5\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c619a283df236d59196f4841f7e65a97\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6097657892ebfb4306344742d6f6ef8\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80c291d6dbbfd4131ebd5aff094ff648\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944908421c0aed40770799f7b85b4fbe\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd50402262d0e21ce865767236c909be\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acbf9fdc7ac1cde307655d1b9d33cc04\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d398d2e65095ff0b4527b9f44f07731\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5577f73a5697542183fc1de286f56e68\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e54342ac2a80389e87550f5a09685556\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c65393cbb7084efc8db11d6325463b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c5c69c82f4f77f6f22c7a62d29e5c27\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c585d5e5ba596e3ec85eafaaf598aead\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5e76f5c29df7ff29548a2fa640284a\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a1039563e226510804fb2acf7c1be89\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa4a5ad8f96fe9db6806d5ed9871e510\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2559c198bf253dbd62b1940a461c4cb\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\902642fa3a7c303f8f29e874c226c910\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\614f52b7ea57ebbb1e43bb7fac47b476\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a02e8502b1bbcdc3eff3db3d2456184f\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4df675822554b90b729c5d08a9075cec\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33616c642f849b4ac16b34b60eb3d18e\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efa51daab58fc0bf4e187c478461c722\transformed\room-ktx-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afff7bf4cd37e1650ec7b0fa4b044ef6\transformed\shimmer-0.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\989760270fa50702dba1c9ac3bed6925\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\519b734d2ca03fc7d71ebf60ff8704ad\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71200b253a907a36e92c442228507757\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c22fc5c5d86ad934e13c6af262111fd\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e97d0f788b1ee8bdc39b4680415a98d\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10558ea352c72b29a4f835f38fa8a3d2\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8472988f58aeca6461c061390f74c134\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\88207f26aa08e8c34826afd58c8661a3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b6aa6024d7026585274a68b5bed354e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03381d2e174a3726bf70c9c16ae9fe1a\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cb32e1c29f8176ef18950891c874ba0\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b057e25c34517c1551186aba4900da0b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8c484383cc882b1a3d8c60e273f58ae\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\888de4576661fab2afe952598ca35f99\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd17f98e89ee006779d972c8fcc7c3e5\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bf31aafbd25b4758531e4af7a09dd20\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5850c55abb7bdbbed2ae58c9a30ef90b\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:10:5-80
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:15:5-16:40
	tools:ignore
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:16:9-37
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:15:22-79
uses-permission#android.permission.VIBRATE
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:19:5-66
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:19:22-63
application
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:21:5-77:19
INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:21:5-77:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a95a1075bc0403a1ca2712bac24841aa\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a95a1075bc0403a1ca2712bac24841aa\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a44f930cd05df25e4d7fb3de1fecf309\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a44f930cd05df25e4d7fb3de1fecf309\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.6.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96d381843322b4f6d1a73e69fba6426c\transformed\lottie-6.6.6\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.6.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96d381843322b4f6d1a73e69fba6426c\transformed\lottie-6.6.6\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8472988f58aeca6461c061390f74c134\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8472988f58aeca6461c061390f74c134\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\88207f26aa08e8c34826afd58c8661a3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\88207f26aa08e8c34826afd58c8661a3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:29:9-35
	android:label
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:27:9-41
	android:fullBackupContent
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:25:9-54
	android:roundIcon
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:28:9-54
	android:icon
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:26:9-43
	android:allowBackup
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:23:9-35
	android:theme
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:30:9-48
	android:dataExtractionRules
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:24:9-65
	android:usesCleartextTraffic
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:31:9-44
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:22:9-46
activity#com.bearmod.loader.ui.splash.SplashActivity
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:34:9-43:20
	android:windowSoftInputMode
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:38:13-56
	android:exported
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:36:13-36
	android:theme
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:37:13-59
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:35:13-53
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:39:13-42:29
action#android.intent.action.MAIN
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:40:17-69
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:40:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:41:17-77
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:41:27-74
activity#com.bearmod.loader.ui.auth.LoginActivity
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:46:9-49:67
	android:exported
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:48:13-37
	android:theme
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:49:13-64
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:47:13-50
activity#com.bearmod.loader.ui.main.MainActivity
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:52:9-55:67
	android:exported
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:54:13-37
	android:theme
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:55:13-64
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:53:13-49
activity#com.bearmod.loader.ui.patch.PatchExecutionActivity
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:58:9-61:67
	android:exported
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:60:13-37
	android:theme
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:61:13-64
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:59:13-60
activity#com.bearmod.loader.ui.download.DownloadActivity
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:64:9-67:67
	android:exported
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:66:13-37
	android:theme
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:67:13-64
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:65:13-57
activity#com.bearmod.loader.ui.settings.SettingsActivity
ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:70:9-73:67
	android:exported
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:73:13-64
	android:name
		ADDED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml:71:13-57
uses-sdk
INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml
INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bb3a81c74d15c56a989d834df172a81\transformed\viewbinding-8.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bb3a81c74d15c56a989d834df172a81\transformed\viewbinding-8.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97afcf84480752d7a418592ccdff1c40\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97afcf84480752d7a418592ccdff1c40\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28f50267faca26958223bb8f925dbd83\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28f50267faca26958223bb8f925dbd83\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b622a2188e71c5a94ec3d3dca0d0346\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b622a2188e71c5a94ec3d3dca0d0346\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b19140129864dc831a67762e70345bb\transformed\navigation-ui-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b19140129864dc831a67762e70345bb\transformed\navigation-ui-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a95a1075bc0403a1ca2712bac24841aa\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a95a1075bc0403a1ca2712bac24841aa\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a44f930cd05df25e4d7fb3de1fecf309\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a44f930cd05df25e4d7fb3de1fecf309\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.6.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96d381843322b4f6d1a73e69fba6426c\transformed\lottie-6.6.6\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.6.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96d381843322b4f6d1a73e69fba6426c\transformed\lottie-6.6.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08eff8b9bceb77a4adcd616411db578c\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08eff8b9bceb77a4adcd616411db578c\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84e22dff9d3244eb5f30d696cec58c96\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84e22dff9d3244eb5f30d696cec58c96\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\225f2e11b6c3a761c794807ea43fee3a\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\225f2e11b6c3a761c794807ea43fee3a\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\043eb9b4860c33e7bc5aeacc39177736\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\043eb9b4860c33e7bc5aeacc39177736\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3037431f73c5541471bc5a03fb28dc9\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3037431f73c5541471bc5a03fb28dc9\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.fragment:fragment-ktx:1.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9af2d3f16075cce64beccbaa636857a7\transformed\fragment-ktx-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9af2d3f16075cce64beccbaa636857a7\transformed\fragment-ktx-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eed853c0261802e4d9e70faf27b55f23\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eed853c0261802e4d9e70faf27b55f23\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b706937324fd53a4b4ebf9d99bfa76c4\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b706937324fd53a4b4ebf9d99bfa76c4\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03a3ec360ca4a6670ac4c7dddb9dd063\transformed\fragment-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03a3ec360ca4a6670ac4c7dddb9dd063\transformed\fragment-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff72eb6b37be78e33325a299cef88a9\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff72eb6b37be78e33325a299cef88a9\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf0d58f032068de07cddaab312ce5c0a\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf0d58f032068de07cddaab312ce5c0a\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ca7aab97726176c596478f9fd1a4002\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ca7aab97726176c596478f9fd1a4002\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057d5f21678a2f8e98dd0c28fac64b68\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057d5f21678a2f8e98dd0c28fac64b68\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4be8df471c2a3e7d594e90aba5476348\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4be8df471c2a3e7d594e90aba5476348\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c45357efb1170dcabe56a0015399a7a\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c45357efb1170dcabe56a0015399a7a\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4650b5b43fed97c925c90064bab87ab5\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4650b5b43fed97c925c90064bab87ab5\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c619a283df236d59196f4841f7e65a97\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c619a283df236d59196f4841f7e65a97\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6097657892ebfb4306344742d6f6ef8\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6097657892ebfb4306344742d6f6ef8\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80c291d6dbbfd4131ebd5aff094ff648\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80c291d6dbbfd4131ebd5aff094ff648\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944908421c0aed40770799f7b85b4fbe\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944908421c0aed40770799f7b85b4fbe\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd50402262d0e21ce865767236c909be\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd50402262d0e21ce865767236c909be\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acbf9fdc7ac1cde307655d1b9d33cc04\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acbf9fdc7ac1cde307655d1b9d33cc04\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d398d2e65095ff0b4527b9f44f07731\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d398d2e65095ff0b4527b9f44f07731\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5577f73a5697542183fc1de286f56e68\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5577f73a5697542183fc1de286f56e68\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e54342ac2a80389e87550f5a09685556\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e54342ac2a80389e87550f5a09685556\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c65393cbb7084efc8db11d6325463b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c65393cbb7084efc8db11d6325463b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c5c69c82f4f77f6f22c7a62d29e5c27\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c5c69c82f4f77f6f22c7a62d29e5c27\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c585d5e5ba596e3ec85eafaaf598aead\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c585d5e5ba596e3ec85eafaaf598aead\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5e76f5c29df7ff29548a2fa640284a\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5e76f5c29df7ff29548a2fa640284a\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a1039563e226510804fb2acf7c1be89\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a1039563e226510804fb2acf7c1be89\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa4a5ad8f96fe9db6806d5ed9871e510\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa4a5ad8f96fe9db6806d5ed9871e510\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2559c198bf253dbd62b1940a461c4cb\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2559c198bf253dbd62b1940a461c4cb\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\902642fa3a7c303f8f29e874c226c910\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\902642fa3a7c303f8f29e874c226c910\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\614f52b7ea57ebbb1e43bb7fac47b476\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\614f52b7ea57ebbb1e43bb7fac47b476\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a02e8502b1bbcdc3eff3db3d2456184f\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a02e8502b1bbcdc3eff3db3d2456184f\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4df675822554b90b729c5d08a9075cec\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4df675822554b90b729c5d08a9075cec\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33616c642f849b4ac16b34b60eb3d18e\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33616c642f849b4ac16b34b60eb3d18e\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efa51daab58fc0bf4e187c478461c722\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efa51daab58fc0bf4e187c478461c722\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afff7bf4cd37e1650ec7b0fa4b044ef6\transformed\shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afff7bf4cd37e1650ec7b0fa4b044ef6\transformed\shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\989760270fa50702dba1c9ac3bed6925\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\989760270fa50702dba1c9ac3bed6925\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\519b734d2ca03fc7d71ebf60ff8704ad\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\519b734d2ca03fc7d71ebf60ff8704ad\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71200b253a907a36e92c442228507757\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71200b253a907a36e92c442228507757\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c22fc5c5d86ad934e13c6af262111fd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c22fc5c5d86ad934e13c6af262111fd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e97d0f788b1ee8bdc39b4680415a98d\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e97d0f788b1ee8bdc39b4680415a98d\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10558ea352c72b29a4f835f38fa8a3d2\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10558ea352c72b29a4f835f38fa8a3d2\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8472988f58aeca6461c061390f74c134\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8472988f58aeca6461c061390f74c134\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\88207f26aa08e8c34826afd58c8661a3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\88207f26aa08e8c34826afd58c8661a3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b6aa6024d7026585274a68b5bed354e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b6aa6024d7026585274a68b5bed354e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03381d2e174a3726bf70c9c16ae9fe1a\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03381d2e174a3726bf70c9c16ae9fe1a\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cb32e1c29f8176ef18950891c874ba0\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cb32e1c29f8176ef18950891c874ba0\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b057e25c34517c1551186aba4900da0b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b057e25c34517c1551186aba4900da0b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8c484383cc882b1a3d8c60e273f58ae\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8c484383cc882b1a3d8c60e273f58ae\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\888de4576661fab2afe952598ca35f99\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\888de4576661fab2afe952598ca35f99\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd17f98e89ee006779d972c8fcc7c3e5\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd17f98e89ee006779d972c8fcc7c3e5\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bf31aafbd25b4758531e4af7a09dd20\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bf31aafbd25b4758531e4af7a09dd20\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5850c55abb7bdbbed2ae58c9a30ef90b\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5850c55abb7bdbbed2ae58c9a30ef90b\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Augment_Code\BearLoader4\app\src\main\AndroidManifest.xml
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8472988f58aeca6461c061390f74c134\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8472988f58aeca6461c061390f74c134\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20a50576a68bdb14563c7f8cdd4d116d\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df94f47acb8e075192c8b652301bb459\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\917dce5a1104aa4505b9385bac6273aa\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f2be59c03f86888596a581fb73e161\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81d5aa67ccc92a49c6a5ceb5d4e9eb3c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\284506b79191e0b8da481236343ba124\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd6235332b3f0520ffbbf7d82b1553bc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
