package com.bearmod.loader.ui.auth;

import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.bearmod.loader.R;
import com.bearmod.loader.auth.AuthResult;
import com.bearmod.loader.auth.DirectKeyAuthManager;

/**
 * Test login activity
 * Automatically attempts to login with a hardcoded license key
 */
public class TestLoginActivity extends AppCompatActivity {

    private static final String TAG = "TestLoginActivity";
    private static final String TEST_LICENSE_KEY = "4PjqHA-QtCw6Q-jyrU9S-0kPxSX-ZO2G3k";
    
    private DirectKeyAuthManager keyAuthManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);
        
        // Initialize KeyAuth manager
        keyAuthManager = DirectKeyAuthManager.getInstance();
        keyAuthManager.initialize(this);
        
        // Automatically attempt login
        testLogin();
    }
    
    /**
     * Test login with hardcoded license key
     */
    private void testLogin() {
        Log.d(TAG, "Testing login with license key: " + TEST_LICENSE_KEY);
        
        // Login with KeyAuth
        keyAuthManager.login(TEST_LICENSE_KEY, new DirectKeyAuthManager.AuthCallback() {
            @Override
            public void onSuccess(AuthResult result) {
                Log.d(TAG, "Login successful!");
                Log.d(TAG, "Expiry date: " + keyAuthManager.formatExpiryDate(result.getExpiryDate()));
                Log.d(TAG, "Registration date: " + result.getRegistrationDate());
                Log.d(TAG, "Remaining days: " + keyAuthManager.getRemainingDays(result.getExpiryDate()));
                
                runOnUiThread(() -> {
                    Toast.makeText(TestLoginActivity.this, 
                            "Login successful! Expiry: " + keyAuthManager.formatExpiryDate(result.getExpiryDate()), 
                            Toast.LENGTH_LONG).show();
                });
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Login failed: " + error);
                
                runOnUiThread(() -> {
                    Toast.makeText(TestLoginActivity.this, 
                            "Login failed: " + error, 
                            Toast.LENGTH_LONG).show();
                });
            }
        });
    }
}
