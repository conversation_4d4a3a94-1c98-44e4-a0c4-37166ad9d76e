<resources>
    <!-- Base application theme using Material Components -->
    <style name="Theme.BearLoader" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">@color/primary_light</item>
        <item name="colorOnPrimaryContainer">@color/background</item>

        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/accent_light</item>
        <item name="colorOnSecondaryContainer">@color/background</item>

        <!-- Surface colors -->
        <item name="colorSurface">@color/card_background</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorSurfaceVariant">@color/background_light</item>
        <item name="colorOnSurfaceVariant">@color/text_secondary</item>

        <!-- Background -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/text_primary</item>

        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/background</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/background</item>
    </style>

    <!-- No Action Bar theme for full-screen activities -->
    <style name="Theme.BearLoader.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

    <!-- Splash screen theme -->
    <style name="Theme.BearLoader.Splash" parent="Theme.BearLoader.NoActionBar">
        <item name="android:windowBackground">@color/background</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>

    <!-- Dialog theme -->
    <style name="Theme.BearLoader.Dialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorAccent">@color/accent</item>
        <item name="android:background">@color/background</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
    </style>

    <!-- Material Components Button styles -->
    <style name="Widget.BearLoader.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="elevation">2dp</item>
    </style>

    <style name="Widget.BearLoader.Button.Secondary" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/primary</item>
        <item name="android:textColor">@color/primary</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Widget.BearLoader.Button.Tonal" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="backgroundTint">@color/primary_light</item>
        <item name="android:textColor">@color/primary</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.BearLoader.Headline6"
        parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TextAppearance.BearLoader.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textSize">@dimen/text_size_medium</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TextAppearance.BearLoader.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="android:textSize">@dimen/text_size_small</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>

    <style name="TextAppearance.BearLoader.Caption"
        parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textSize">@dimen/text_size_caption</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>

        <!-- Add this missing style -->
    <style name="Theme.BearLoader.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.BearLoader.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />


    <!-- <style name="Widget.BearLoader.Button.Secondary"
        parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/primary</item>
        <item name="android:textColor">@color/primary</item>
        <item name="cornerRadius">8dp</item>
    </style> -->
</resources>