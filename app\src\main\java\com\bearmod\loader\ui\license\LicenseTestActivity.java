package com.bearmod.loader.ui.license;

import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

import com.bearmod.loader.R;
import com.bearmod.loader.auth.AuthResult;
import com.bearmod.loader.auth.DirectKeyAuthManager;
import com.bearmod.loader.auth.HWID;
import com.bearmod.loader.databinding.ActivityLicenseTestBinding;

import java.text.SimpleDateFormat;
import java.util.Locale;

/**
 * Activity for testing license server connection and license validation
 */
public class LicenseTestActivity extends AppCompatActivity {

    private ActivityLicenseTestBinding binding;
    private DirectKeyAuthManager keyAuthManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Initialize view binding
        binding = ActivityLicenseTestBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // Set up toolbar
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("License Test");
        }

        // Get KeyAuth manager instance
        keyAuthManager = DirectKeyAuthManager.getInstance();

        // Set up test connection button
        binding.btnTestConnection.setOnClickListener(v -> testConnection());

        // Set up validate license button
        binding.btnValidateLicense.setOnClickListener(v -> validateLicense());

        // Display server information
        displayServerInfo();
    }

    /**
     * Display server information
     */
    private void displayServerInfo() {
        // Display primary URL
        binding.tvPrimaryUrl.setText("Primary URL: " + DirectKeyAuthManager.getApiUrl());

        // Display alternate URL
        binding.tvAlternateUrl.setText("Alternate URL: " + DirectKeyAuthManager.getAlternateApiUrl());

        // Display device ID
        binding.tvDeviceId.setText("Device ID: " + HWID.getHWID());
    }

    /**
     * Test connection to license server
     */
    private void testConnection() {
        // Show progress
        binding.progressBar.setVisibility(View.VISIBLE);
        binding.tvConnectionStatus.setVisibility(View.GONE);

        // Test connection
        keyAuthManager.testConnection(new DirectKeyAuthManager.AuthCallback() {
            @Override
            public void onSuccess(AuthResult result) {
                // Hide progress
                binding.progressBar.setVisibility(View.GONE);

                // Show success message
                binding.tvConnectionStatus.setText(result.getMessage());
                binding.tvConnectionStatus.setTextColor(ContextCompat.getColor(LicenseTestActivity.this, R.color.success));
                binding.tvConnectionStatus.setVisibility(View.VISIBLE);
            }

            @Override
            public void onError(String error) {
                // Hide progress
                binding.progressBar.setVisibility(View.GONE);

                // Show error message
                binding.tvConnectionStatus.setText("Connection failed: " + error);
                binding.tvConnectionStatus.setTextColor(ContextCompat.getColor(LicenseTestActivity.this, R.color.error));
                binding.tvConnectionStatus.setVisibility(View.VISIBLE);
            }
        });
    }

    /**
     * Validate license
     */
    private void validateLicense() {
        // Get license key
        String licenseKey = binding.etLicenseKey.getText().toString().trim();

        // Check if license key is empty
        if (licenseKey.isEmpty()) {
            Toast.makeText(this, "Please enter a license key", Toast.LENGTH_SHORT).show();
            return;
        }

        // Show progress
        binding.progressBar.setVisibility(View.VISIBLE);
        binding.tvLicenseStatus.setVisibility(View.GONE);

        // Validate license
        keyAuthManager.login(licenseKey, new DirectKeyAuthManager.AuthCallback() {
            @Override
            public void onSuccess(AuthResult result) {
                // Hide progress
                binding.progressBar.setVisibility(View.GONE);

                // Format expiry date
                String expiryDate = "Unknown";
                if (result.getExpiryDate() != null) {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
                    expiryDate = dateFormat.format(result.getExpiryDate());
                }

                // Show success message
                binding.tvLicenseStatus.setText("License is valid until " + expiryDate);
                binding.tvLicenseStatus.setTextColor(ContextCompat.getColor(LicenseTestActivity.this, R.color.success));
                binding.tvLicenseStatus.setVisibility(View.VISIBLE);
            }

            @Override
            public void onError(String error) {
                // Hide progress
                binding.progressBar.setVisibility(View.GONE);

                // Show error message
                binding.tvLicenseStatus.setText("License validation failed: " + error);
                binding.tvLicenseStatus.setTextColor(ContextCompat.getColor(LicenseTestActivity.this, R.color.error));
                binding.tvLicenseStatus.setVisibility(View.VISIBLE);
            }
        });
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
