<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.auth.LoginActivity">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="80dp"
        android:contentDescription="@string/app_logo"
        android:src="@drawable/logo_splash"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/app_name"
        android:textColor="@color/text_primary"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_logo" />

    <TextView
        android:id="@+id/tv_login_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/login_subtitle"
        android:textColor="@color/text_secondary"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_app_name" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_login"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="48dp"
        android:layout_marginEnd="24dp"
        app:cardBackgroundColor="@color/card_background"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_login_subtitle">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/license_key"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_license_key"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="@string/enter_license_key"
                app:boxBackgroundColor="@color/background_light"
                app:boxStrokeColor="@color/primary"
                app:errorEnabled="true"
                app:errorIconDrawable="@drawable/ic_error"
                app:errorTextColor="@color/error"
                app:hintTextColor="@color/text_hint"
                app:startIconDrawable="@drawable/ic_key"
                app:startIconTint="@color/primary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_license_key"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionDone"
                    android:inputType="textVisiblePassword"
                    android:maxLines="1"
                    android:textAllCaps="true"
                    android:textColor="@color/text_primary"
                    tools:ignore="TextContrastCheck,VisualLintTextFieldSize" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.switchmaterial.SwitchMaterial
                android:id="@+id/switch_remember"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:checked="true"
                android:text="@string/remember_me"
                android:textColor="@color/text_secondary"
                tools:ignore="VisualLintButtonSize" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_login"
                style="@style/Widget.BearLoader.Button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:enabled="false"
                android:text="@string/login"
                app:cornerRadius="8dp"
                app:elevation="4dp"
                app:rippleColor="@color/ripple"
                tools:ignore="VisualLintButtonSize" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/tv_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/version_info"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- Loading progress -->
    <ProgressBar
        android:id="@+id/progress_login"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ProgressBar
        android:id="@+id/progress_initializing"
        style="?android:attr/progressBarStyleSmall"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/success_animation"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:lottie_autoPlay="false"
        app:lottie_loop="false"
        app:lottie_rawRes="@raw/success_animation" />


    <View
        android:id="@+id/view_overlay"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#80000000"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
