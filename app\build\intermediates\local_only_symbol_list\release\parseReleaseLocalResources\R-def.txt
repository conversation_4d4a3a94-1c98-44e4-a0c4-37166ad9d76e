R_DEF: Internal format may change without notice
local
color accent
color accent_dark
color background
color background_light
color black
color card_background
color divider
color error
color info
color primary
color primary_dark
color primary_light
color progress_background
color purple_200
color purple_500
color purple_700
color ripple
color shimmer_color
color success
color teal_200
color teal_700
color text_hint
color text_primary
color text_secondary
color warning
color white
dimen margin_medium
dimen margin_small
dimen text_size_caption
dimen text_size_large
dimen text_size_medium
dimen text_size_small
drawable app_logo
drawable bg_status
drawable ic_app_icon
drawable ic_back
drawable ic_check_circle
drawable ic_clear_cache
drawable ic_dashboard
drawable ic_download
drawable ic_empty
drawable ic_error
drawable ic_key
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_logout
drawable ic_reset
drawable ic_settings
drawable logo
drawable logo_splash
drawable splash_background
id animation_download_complete
id btn_apply_patch
id btn_cancel_download
id btn_download
id btn_download_patches
id btn_login
id btn_logout
id btn_scan_offsets
id btn_start_patching
id card_download_info
id card_download_progress
id card_login
id card_logs
id card_mode
id card_release
id card_target_app
id drawer_layout
id et_license_key
id iv_download
id iv_logo
id layout_clear_cache
id layout_empty
id layout_reset_config
id nav_dashboard
id nav_download
id nav_keyauth_test
id nav_logout
id nav_settings
id nav_view
id progressBar
id progress_bar
id progress_download
id progress_initializing
id progress_loading
id progress_login
id progress_patching
id radio_group_mode
id radio_non_root
id radio_root
id ripple_effect
id rv_patches
id rv_releases
id shimmer_button
id shimmer_date
id shimmer_description
id shimmer_layout
id shimmer_status
id shimmer_title
id shimmer_version
id spinner_target_app
id success_animation
id swipe_refresh
id switch_auto_login
id switch_remember
id switch_stealth
id til_license_key
id toolbar
id tv_apk_size
id tv_app_name
id tv_app_version
id tv_available_patches
id tv_available_releases
id tv_download_percentage
id tv_download_status
id tv_game_version
id tv_license_info
id tv_login_subtitle
id tv_logs
id tv_logs_title
id tv_no_releases
id tv_obb_size
id tv_patch_description
id tv_patch_name
id tv_release_date
id tv_release_description
id tv_release_name
id tv_status
id tv_total_size
id tv_update_date
id tv_version
id view_overlay
layout activity_download
layout activity_login
layout activity_main
layout activity_patch_execution
layout activity_settings
layout activity_splash
layout item_patch
layout item_patch_shimmer
layout item_release
layout nav_header
menu drawer_menu
mipmap ic_launcher
mipmap ic_launcher_round
raw download_complete
raw success_animation
string about
string about_description
string apk_size
string app_logo
string app_name
string app_settings
string app_version
string app_version_static
string apply_patch
string auto_login
string available_patches
string available_releases
string cache_cleared
string cancel
string cancel_download
string clear_cache
string clear_cache_confirm
string config_reset
string continue_anyway
string continue_download
string dark_mode
string dashboard_title
string days_remaining
string download_cancelled
string download_complete
string download_complete_path
string download_failed
string download_in_progress
string download_patches
string download_progress
string download_title
string downloading
string downloading_detailed
string enter_license_key
string error
string execution_logs
string execution_mode
string exit
string game_version
string info
string invalid_license_key_format
string keyauth_init_failed
string keyauth_init_warning
string language
string license_key
string license_valid_until
string loading
string login
string login_error
string login_subtitle
string login_success
string logout
string logout_confirm
string navigation_drawer_close
string navigation_drawer_open
string no_patches_available
string no_releases_available
string non_root_mode
string not_installed
string notification_settings
string obb_size
string ok
string patch_execution
string patch_settings
string patch_status
string patches_updated
string patching_complete
string patching_failed
string patching_in_progress
string registration_date
string released
string remember_me
string reset_config
string reset_config_confirm
string retry
string root_mode
string scan_offsets
string scanning_offsets
string scanning_offsets_for
string select_release_first
string select_target
string settings_title
string start_patching
string stop_patching
string success
string sync_error
string target_app
string toggle_stealth
string total_size
string up_to_date
string update_available
string updated
string version_info
string warning
style TextAppearance.BearLoader.Body1
style TextAppearance.BearLoader.Body2
style TextAppearance.BearLoader.Caption
style TextAppearance.BearLoader.Headline6
style Theme.BearLoader
style Theme.BearLoader.AppBarOverlay
style Theme.BearLoader.Dialog
style Theme.BearLoader.NoActionBar
style Theme.BearLoader.PopupOverlay
style Theme.BearLoader.Splash
style Widget.BearLoader.Button
style Widget.BearLoader.Button.Secondary
xml backup_rules
xml data_extraction_rules
