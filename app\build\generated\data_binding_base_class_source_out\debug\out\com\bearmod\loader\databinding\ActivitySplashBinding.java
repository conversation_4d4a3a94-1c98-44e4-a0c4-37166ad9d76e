// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.VideoView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySplashBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final ConstraintLayout overlayContainer;

  @NonNull
  public final CircularProgressIndicator progressBar;

  @NonNull
  public final TextView tvAppName;

  @NonNull
  public final TextView tvAppVersion;

  @NonNull
  public final TextView tvLoading;

  @NonNull
  public final View videoLoadingOverlay;

  @NonNull
  public final VideoView videoSplash;

  private ActivitySplashBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView ivLogo,
      @NonNull ConstraintLayout overlayContainer, @NonNull CircularProgressIndicator progressBar,
      @NonNull TextView tvAppName, @NonNull TextView tvAppVersion, @NonNull TextView tvLoading,
      @NonNull View videoLoadingOverlay, @NonNull VideoView videoSplash) {
    this.rootView = rootView;
    this.ivLogo = ivLogo;
    this.overlayContainer = overlayContainer;
    this.progressBar = progressBar;
    this.tvAppName = tvAppName;
    this.tvAppVersion = tvAppVersion;
    this.tvLoading = tvLoading;
    this.videoLoadingOverlay = videoLoadingOverlay;
    this.videoSplash = videoSplash;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_splash, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySplashBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_logo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.overlay_container;
      ConstraintLayout overlayContainer = ViewBindings.findChildViewById(rootView, id);
      if (overlayContainer == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      CircularProgressIndicator progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_app_name;
      TextView tvAppName = ViewBindings.findChildViewById(rootView, id);
      if (tvAppName == null) {
        break missingId;
      }

      id = R.id.tv_app_version;
      TextView tvAppVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvAppVersion == null) {
        break missingId;
      }

      id = R.id.tv_loading;
      TextView tvLoading = ViewBindings.findChildViewById(rootView, id);
      if (tvLoading == null) {
        break missingId;
      }

      id = R.id.video_loading_overlay;
      View videoLoadingOverlay = ViewBindings.findChildViewById(rootView, id);
      if (videoLoadingOverlay == null) {
        break missingId;
      }

      id = R.id.video_splash;
      VideoView videoSplash = ViewBindings.findChildViewById(rootView, id);
      if (videoSplash == null) {
        break missingId;
      }

      return new ActivitySplashBinding((ConstraintLayout) rootView, ivLogo, overlayContainer,
          progressBar, tvAppName, tvAppVersion, tvLoading, videoLoadingOverlay, videoSplash);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
