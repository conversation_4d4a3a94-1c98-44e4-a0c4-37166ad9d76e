<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_release" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\item_release.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/card_release"><Targets><Target id="@+id/card_release" tag="layout/item_release_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="163" endOffset="51"/></Target><Target id="@+id/iv_release_icon" view="ImageView"><Expressions/><location startLine="24" startOffset="8" endLine="34" endOffset="39"/></Target><Target id="@+id/tv_release_name" view="TextView"><Expressions/><location startLine="36" startOffset="8" endLine="49" endOffset="44"/></Target><Target id="@+id/tv_release_version" view="TextView"><Expressions/><location startLine="51" startOffset="8" endLine="63" endOffset="40"/></Target><Target id="@+id/chip_status" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="66" startOffset="8" endLine="78" endOffset="36"/></Target><Target id="@+id/tv_release_description" view="TextView"><Expressions/><location startLine="80" startOffset="8" endLine="91" endOffset="121"/></Target><Target id="@+id/chip_group_info" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="94" startOffset="8" endLine="146" endOffset="52"/></Target><Target id="@+id/chip_game_version" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="105" startOffset="12" endLine="116" endOffset="42"/></Target><Target id="@+id/chip_release_date" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="118" startOffset="12" endLine="129" endOffset="41"/></Target><Target id="@+id/chip_file_size" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="131" startOffset="12" endLine="144" endOffset="38"/></Target><Target id="@+id/ripple_effect" view="View"><Expressions/><location startLine="149" startOffset="8" endLine="159" endOffset="55"/></Target></Targets></Layout>