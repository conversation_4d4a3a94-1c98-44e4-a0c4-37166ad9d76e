package com.bearmod.loader.ui.splash;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.bearmod.loader.BearLoaderApplication;
import com.bearmod.loader.R;
import com.bearmod.loader.databinding.ActivitySplashBinding;
import com.bearmod.loader.ui.auth.LoginActivity;
import com.bearmod.loader.ui.license.LicenseTestActivity;
import com.bearmod.loader.ui.test.DrawerTestActivity;
import com.bearmod.loader.util.BuildValidator;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;

import java.util.List;

/**
 * Splash activity
 * Displays splash screen and validates build
 */
public class SplashActivity extends AppCompatActivity {

    private ActivitySplashBinding binding;
    private static final long SPLASH_DURATION = 2000; // 2 seconds

    // Dialog reference to prevent multiple dialogs
    private AlertDialog mDevModeDialog = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Initialize view binding
        binding = ActivitySplashBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        Log.d("SplashActivity", "onCreate called");

        // Animate logo
        animateLogo();

        // Validate build after a short delay
        new Handler(Looper.getMainLooper()).postDelayed(this::validateBuild, 1000);

        // Force proceed to login after a timeout if nothing happens
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            // Check if we're still in the SplashActivity and no dialog is showing
            if (!isFinishing() && !isDestroyed() && (mDevModeDialog == null || !mDevModeDialog.isShowing())) {
                Log.d("SplashActivity", "Force proceeding to development mode dialog after timeout");
                // Force show development mode dialog
                showDevelopmentModeDialog(true);
            }
        }, 5000); // 5 second timeout
    }

    /**
     * Show development mode dialog
     * @param forced Whether this is a forced dialog due to timeout
     */
    private void showDevelopmentModeDialog(boolean forced) {
        // Dismiss any existing dialog
        if (mDevModeDialog != null && mDevModeDialog.isShowing()) {
            try {
                mDevModeDialog.dismiss();
            } catch (Exception e) {
                Log.e("SplashActivity", "Error dismissing existing dialog: " + e.getMessage());
            }
        }

        // In development mode, show a dialog to choose which activity to launch
        final String[] options = new String[]{"Main UI (Drawer)", "License Test"};
        Log.d("SplashActivity", (forced ? "Forcing" : "Showing") + " development mode dialog with options");

        try {
            MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(this);

            if (forced) {
                builder.setTitle("Development Mode (Forced)")
                       .setMessage("The app was stuck at the splash screen, so this dialog was forced to appear.");
            } else {
                builder.setTitle("Development Mode");
            }

            builder.setItems(options, (dialog, which) -> {
                dialog.dismiss();

                Intent intent;
                try {
                    switch (which) {
                        case 1: // License Test
                            Log.d("SplashActivity", "Selected License Test");
                            Toast.makeText(SplashActivity.this, "Starting License Test Activity", Toast.LENGTH_SHORT).show();
                            intent = new Intent(SplashActivity.this, LicenseTestActivity.class);
                            break;
                        case 0: // Main UI
                        default:
                            Log.d("SplashActivity", "Selected Main UI (Drawer)");
                            Toast.makeText(SplashActivity.this, "Starting Drawer Test Activity", Toast.LENGTH_SHORT).show();
                            intent = new Intent(SplashActivity.this, DrawerTestActivity.class);
                            break;
                    }

                    startActivity(intent);

                    // Apply fade out transition
                    overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);

                    // Finish splash activity
                    finish();
                } catch (Exception e) {
                    Log.e("SplashActivity", "Error starting activity: " + e.getMessage(), e);
                    Toast.makeText(SplashActivity.this, "Error: " + e.getMessage(), Toast.LENGTH_LONG).show();
                }
            });

            builder.setCancelable(false);

            // Create and show the dialog
            mDevModeDialog = builder.create();
            mDevModeDialog.show();
        } catch (Exception e) {
            Log.e("SplashActivity", "Error showing development mode dialog: " + e.getMessage(), e);
            // Fallback to login activity if dialog fails
            Toast.makeText(this, "Error showing dialog: " + e.getMessage(), Toast.LENGTH_LONG).show();
            startLoginActivity();
        }
    }

    /**
     * Animate logo
     */
    private void animateLogo() {
        // Fade in animation
        AlphaAnimation fadeIn = new AlphaAnimation(0.0f, 1.0f);
        fadeIn.setDuration(1000);
        fadeIn.setFillAfter(true);

        // Start animation
        binding.ivLogo.startAnimation(fadeIn);

        // Set animation listener
        fadeIn.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                // Animation started
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                // Start progress animation
                binding.progressBar.setVisibility(View.VISIBLE);

                // Fade in progress animation
                AlphaAnimation fadeInProgress = new AlphaAnimation(0.0f, 1.0f);
                fadeInProgress.setDuration(500);
                fadeInProgress.setFillAfter(true);
                binding.progressBar.startAnimation(fadeInProgress);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
                // Animation repeated
            }
        });
    }

    /**
     * Validate build
     */
    private void validateBuild() {
        // Add debug logging
        Log.d("SplashActivity", "Starting build validation");

        // Validate build
        BuildValidator.ValidationResult result = BuildValidator.validateBuild(this);

        if (result.isValid()) {
            // Check for warnings
            if (result.hasWarnings()) {
                // Show warnings
                Log.d("SplashActivity", "Build validation has warnings: " + result.getWarnings().size());
                showWarnings(result.getWarnings());
            } else {
                // No warnings, proceed to login
                Log.d("SplashActivity", "Build validation successful, proceeding to login");
                proceedToLogin();
            }
        } else {
            // Show errors
            Log.e("SplashActivity", "Build validation failed with errors: " + result.getErrors().size());
            showErrors(result.getErrors());
        }
    }

    /**
     * Show errors
     * @param errors List of errors
     */
    private void showErrors(List<String> errors) {
        // Build error message
        StringBuilder errorMessage = new StringBuilder();
        for (String error : errors) {
            errorMessage.append("• ").append(error).append("\n");
        }

        // Show error dialog
        new MaterialAlertDialogBuilder(this)
                .setTitle(R.string.error)
                .setMessage(errorMessage.toString())
                .setPositiveButton(R.string.exit, (dialog, which) -> {
                    dialog.dismiss();
                    finish();
                })
                .setCancelable(false)
                .show();
    }

    /**
     * Show warnings
     * @param warnings List of warnings
     */
    private void showWarnings(List<String> warnings) {
        // Build warning message
        StringBuilder warningMessage = new StringBuilder();
        for (String warning : warnings) {
            warningMessage.append("• ").append(warning).append("\n");
        }

        // Show warning dialog
        new MaterialAlertDialogBuilder(this)
                .setTitle(R.string.warning)
                .setMessage(warningMessage.toString())
                .setPositiveButton(R.string.continue_anyway, (dialog, which) -> {
                    dialog.dismiss();
                    proceedToLogin();
                })
                .setNegativeButton(R.string.exit, (dialog, which) -> {
                    dialog.dismiss();
                    finish();
                })
                .setCancelable(false)
                .show();
    }

    /**
     * Proceed to login or main activity
     */
    private void proceedToLogin() {
        Log.d("SplashActivity", "Proceeding to login after delay of " + SPLASH_DURATION + "ms");

        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            // Check if development mode is enabled
            boolean devMode = BearLoaderApplication.getInstance().isDevelopmentMode();
            Log.d("SplashActivity", "Development mode is: " + devMode);

            if (devMode) {
                // In development mode, show a dialog to choose which activity to launch
                showDevelopmentModeDialog(false);

                // Don't proceed with the normal flow since we're showing a dialog
                return;
            } else {
                // Start login activity
                startLoginActivity();
            }
        }, SPLASH_DURATION);
    }

    /**
     * Start login activity
     */
    private void startLoginActivity() {
        Log.d("SplashActivity", "Starting LoginActivity");
        try {
            // Start login activity
            Intent intent = new Intent(SplashActivity.this, LoginActivity.class);
            startActivity(intent);

            // Apply fade out transition
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);

            // Finish splash activity
            finish();
        } catch (Exception e) {
            Log.e("SplashActivity", "Error starting LoginActivity: " + e.getMessage(), e);
            Toast.makeText(this, "Error starting login: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    @Override
    protected void onDestroy() {
        // Dismiss any dialogs to prevent window leaks
        if (mDevModeDialog != null && mDevModeDialog.isShowing()) {
            try {
                mDevModeDialog.dismiss();
            } catch (Exception e) {
                Log.e("SplashActivity", "Error dismissing dialog on destroy: " + e.getMessage());
            }
        }
        mDevModeDialog = null;

        super.onDestroy();
    }
}
