package com.bearmod.loader.ui.splash;

import android.content.Intent;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.Toast;
import android.widget.VideoView;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.bearmod.loader.BearLoaderApplication;
import com.bearmod.loader.R;
import com.bearmod.loader.databinding.ActivitySplashBinding;
import com.bearmod.loader.ui.auth.LoginActivity;

import com.bearmod.loader.util.BuildValidator;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;

import java.util.List;

/**
 * Splash activity
 * Displays splash screen and validates build
 */
public class SplashActivity extends AppCompatActivity {

    private ActivitySplashBinding binding;
    private VideoView videoView;
    private boolean videoCompleted = false;
    private boolean validationCompleted = false;
    private boolean shouldProceed = false;

    private static final long VIDEO_DURATION = 6000; // 6 seconds fallback
    private static final long FALLBACK_DURATION = 3000; // 3 seconds fallback



    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Initialize view binding
        binding = ActivitySplashBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        Log.d("SplashActivity", "onCreate called");

        // Initialize video view
        videoView = binding.videoSplash;

        // Setup and start video
        setupVideo();

        // Validate build after a short delay
        new Handler(Looper.getMainLooper()).postDelayed(this::validateBuild, 1000);
    }



    /**
     * Setup and start video playback
     */
    private void setupVideo() {
        try {
            // Set video URI to the Bear intro video
            Uri videoUri = Uri.parse("android.resource://" + getPackageName() + "/" + R.raw.bear_intro_mobile);
            videoView.setVideoURI(videoUri);

            // Set video completion listener
            videoView.setOnCompletionListener(mp -> {
                Log.d("SplashActivity", "Video playback completed");
                videoCompleted = true;
                onVideoCompleted();
            });

            // Set video error listener
            videoView.setOnErrorListener((mp, what, extra) -> {
                Log.e("SplashActivity", "Video playback error: " + what + ", " + extra);
                showFallbackAnimation();
                return true;
            });

            // Set video prepared listener
            videoView.setOnPreparedListener(mp -> {
                Log.d("SplashActivity", "Video prepared, starting playback");

                // Hide loading overlay
                binding.videoLoadingOverlay.animate()
                        .alpha(0.0f)
                        .setDuration(500)
                        .withEndAction(() -> binding.videoLoadingOverlay.setVisibility(View.GONE))
                        .start();

                // Start video
                videoView.start();

                // Show overlay content with animation
                showOverlayContent();
            });

            // Prepare video
            videoView.requestFocus();

        } catch (Exception e) {
            Log.e("SplashActivity", "Error setting up video: " + e.getMessage(), e);
            showFallbackAnimation();
        }
    }

    /**
     * Show overlay content with animations
     */
    private void showOverlayContent() {
        // Animate app name
        binding.tvAppName.animate()
                .alpha(1.0f)
                .setDuration(1000)
                .setStartDelay(1500)
                .start();

        // Animate app version
        binding.tvAppVersion.animate()
                .alpha(1.0f)
                .setDuration(800)
                .setStartDelay(2000)
                .start();

        // Animate progress bar
        binding.progressBar.animate()
                .alpha(1.0f)
                .setDuration(600)
                .setStartDelay(2500)
                .start();

        // Animate loading text
        binding.tvLoading.animate()
                .alpha(1.0f)
                .setDuration(600)
                .setStartDelay(2800)
                .start();
    }

    /**
     * Show fallback animation if video fails
     */
    private void showFallbackAnimation() {
        Log.d("SplashActivity", "Showing fallback animation");

        // Hide video view
        videoView.setVisibility(View.GONE);

        // Show fallback logo
        binding.ivLogo.animate()
                .alpha(1.0f)
                .setDuration(1000)
                .start();

        // Show overlay content
        showOverlayContent();

        // Set fallback completion timer
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            videoCompleted = true;
            onVideoCompleted();
        }, FALLBACK_DURATION);
    }

    /**
     * Handle video completion
     */
    private void onVideoCompleted() {
        Log.d("SplashActivity", "Video completed, validation completed: " + validationCompleted);

        if (validationCompleted && shouldProceed) {
            // Both video and validation are complete, proceed to login
            proceedToLogin();
        }
    }

    /**
     * Validate build
     */
    private void validateBuild() {
        // Add debug logging
        Log.d("SplashActivity", "Starting build validation");

        // Validate build
        BuildValidator.ValidationResult result = BuildValidator.validateBuild(this);

        if (result.isValid()) {
            // Check for warnings
            if (result.hasWarnings()) {
                // Show warnings
                Log.d("SplashActivity", "Build validation has warnings: " + result.getWarnings().size());
                showWarnings(result.getWarnings());
            } else {
                // No warnings, mark validation as complete
                Log.d("SplashActivity", "Build validation successful");
                validationCompleted = true;
                shouldProceed = true;

                // Check if video is also complete
                if (videoCompleted) {
                    proceedToLogin();
                }
            }
        } else {
            // Show errors
            Log.e("SplashActivity", "Build validation failed with errors: " + result.getErrors().size());
            showErrors(result.getErrors());
        }
    }

    /**
     * Show errors
     * @param errors List of errors
     */
    private void showErrors(List<String> errors) {
        // Build error message
        StringBuilder errorMessage = new StringBuilder();
        for (String error : errors) {
            errorMessage.append("• ").append(error).append("\n");
        }

        // Show error dialog
        new MaterialAlertDialogBuilder(this)
                .setTitle(R.string.error)
                .setMessage(errorMessage.toString())
                .setPositiveButton(R.string.exit, (dialog, which) -> {
                    dialog.dismiss();
                    finish();
                })
                .setCancelable(false)
                .show();
    }

    /**
     * Show warnings
     * @param warnings List of warnings
     */
    private void showWarnings(List<String> warnings) {
        // Build warning message
        StringBuilder warningMessage = new StringBuilder();
        for (String warning : warnings) {
            warningMessage.append("• ").append(warning).append("\n");
        }

        // Show warning dialog
        new MaterialAlertDialogBuilder(this)
                .setTitle(R.string.warning)
                .setMessage(warningMessage.toString())
                .setPositiveButton(R.string.continue_anyway, (dialog, which) -> {
                    dialog.dismiss();
                    validationCompleted = true;
                    shouldProceed = true;

                    // Check if video is also complete
                    if (videoCompleted) {
                        proceedToLogin();
                    }
                })
                .setNegativeButton(R.string.exit, (dialog, which) -> {
                    dialog.dismiss();
                    finish();
                })
                .setCancelable(false)
                .show();
    }

    /**
     * Proceed to login or main activity
     */
    private void proceedToLogin() {
        Log.d("SplashActivity", "Both video and validation completed, proceeding to login");

        // Add a small delay for smooth transition
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            // Start login activity
            startLoginActivity();
        }, 500);
    }

    /**
     * Start login activity
     */
    private void startLoginActivity() {
        Log.d("SplashActivity", "Starting LoginActivity");
        try {
            // Start login activity
            Intent intent = new Intent(SplashActivity.this, LoginActivity.class);
            startActivity(intent);

            // Apply fade out transition
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);

            // Finish splash activity
            finish();
        } catch (Exception e) {
            Log.e("SplashActivity", "Error starting LoginActivity: " + e.getMessage(), e);
            Toast.makeText(this, "Error starting login: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Clean up video resources
        if (videoView != null && videoView.isPlaying()) {
            videoView.stopPlayback();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

        // Pause video if playing
        if (videoView != null && videoView.isPlaying()) {
            videoView.pause();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        // Resume video if it was playing
        if (videoView != null && !videoCompleted) {
            videoView.start();
        }
    }
}
