[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_app_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_app_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_reset.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_reset.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\layout_nav_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\layout\\nav_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_logout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_logout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_bg_status.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\bg_status.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_splash_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\splash_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_app_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\app_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\layout_item_release.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\layout\\item_release.xml"}, {"merged": "com.bearmod.loader.app-debug-57:/menu_drawer_menu.xml.flat", "source": "com.bearmod.loader.app-main-59:/menu/drawer_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\layout_activity_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\layout\\activity_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\raw_download_complete.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\raw\\download_complete.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\layout_item_patch.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\layout\\item_patch.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\layout_activity_patch_execution.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\layout\\activity_patch_execution.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_logo_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\logo_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_clear_cache.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_clear_cache.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_key.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_key.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_check_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_check_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\layout_activity_download.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\layout\\activity_download.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_empty.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_empty.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\layout_activity_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\layout\\activity_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_error.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\layout_item_patch_shimmer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\layout\\item_patch_shimmer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\menu_drawer_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\menu\\drawer_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\drawable_ic_download.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\drawable\\ic_download.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-debug-57:\\raw_success_animation.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.bearmod.loader.app-main-59:\\raw\\success_animation.json"}]