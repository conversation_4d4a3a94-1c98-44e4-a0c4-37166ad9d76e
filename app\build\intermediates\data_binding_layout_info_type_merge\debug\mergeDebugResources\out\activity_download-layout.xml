<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_download" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_download_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="225" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="19" endOffset="43"/></Target><Target id="@+id/tv_available_releases" view="TextView"><Expressions/><location startLine="21" startOffset="4" endLine="32" endOffset="60"/></Target><Target id="@+id/rv_releases" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="34" startOffset="4" endLine="47" endOffset="47"/></Target><Target id="@+id/progress_loading" view="ProgressBar"><Expressions/><location startLine="49" startOffset="4" endLine="57" endOffset="74"/></Target><Target id="@+id/tv_no_releases" view="TextView"><Expressions/><location startLine="59" startOffset="4" endLine="70" endOffset="74"/></Target><Target id="@+id/card_download_info" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="72" startOffset="4" endLine="139" endOffset="55"/></Target><Target id="@+id/tv_apk_size" view="TextView"><Expressions/><location startLine="101" startOffset="12" endLine="108" endOffset="48"/></Target><Target id="@+id/tv_obb_size" view="TextView"><Expressions/><location startLine="110" startOffset="12" endLine="117" endOffset="49"/></Target><Target id="@+id/tv_total_size" view="TextView"><Expressions/><location startLine="119" startOffset="12" endLine="127" endOffset="51"/></Target><Target id="@+id/btn_download" view="Button"><Expressions/><location startLine="129" startOffset="12" endLine="135" endOffset="57"/></Target><Target id="@+id/card_download_progress" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="141" startOffset="4" endLine="210" endOffset="55"/></Target><Target id="@+id/tv_download_status" view="TextView"><Expressions/><location startLine="171" startOffset="12" endLine="178" endOffset="88"/></Target><Target id="@+id/progress_download" view="ProgressBar"><Expressions/><location startLine="180" startOffset="12" endLine="188" endOffset="55"/></Target><Target id="@+id/tv_download_percentage" view="TextView"><Expressions/><location startLine="190" startOffset="12" endLine="198" endOffset="34"/></Target><Target id="@+id/btn_cancel_download" view="Button"><Expressions/><location startLine="200" startOffset="12" endLine="206" endOffset="56"/></Target><Target id="@+id/animation_download_complete" view="com.airbnb.lottie.LottieAnimationView"><Expressions/><location startLine="212" startOffset="4" endLine="223" endOffset="52"/></Target></Targets></Layout>