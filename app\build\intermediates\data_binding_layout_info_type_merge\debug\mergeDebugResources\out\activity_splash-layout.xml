<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_splash" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_splash.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_splash_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="131" endOffset="51"/></Target><Target id="@+id/video_splash" view="VideoView"><Expressions/><location startLine="10" startOffset="4" endLine="20" endOffset="51"/></Target><Target id="@+id/overlay_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="23" startOffset="4" endLine="117" endOffset="55"/></Target><Target id="@+id/iv_logo" view="ImageView"><Expressions/><location startLine="34" startOffset="8" endLine="45" endOffset="54"/></Target><Target id="@+id/tv_app_name" view="TextView"><Expressions/><location startLine="48" startOffset="8" endLine="65" endOffset="59"/></Target><Target id="@+id/tv_app_version" view="TextView"><Expressions/><location startLine="68" startOffset="8" endLine="84" endOffset="68"/></Target><Target id="@+id/progress_bar" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="87" startOffset="8" endLine="100" endOffset="71"/></Target><Target id="@+id/tv_loading" view="TextView"><Expressions/><location startLine="103" startOffset="8" endLine="115" endOffset="69"/></Target><Target id="@+id/video_loading_overlay" view="View"><Expressions/><location startLine="120" startOffset="4" endLine="129" endOffset="51"/></Target></Targets></Layout>