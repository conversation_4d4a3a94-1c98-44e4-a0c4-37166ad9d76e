<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_splash" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_splash.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_splash_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="130" endOffset="51"/></Target><Target id="@+id/video_splash" view="VideoView"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/overlay_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="22" startOffset="4" endLine="116" endOffset="55"/></Target><Target id="@+id/iv_logo" view="ImageView"><Expressions/><location startLine="33" startOffset="8" endLine="44" endOffset="54"/></Target><Target id="@+id/tv_app_name" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="64" endOffset="59"/></Target><Target id="@+id/tv_app_version" view="TextView"><Expressions/><location startLine="67" startOffset="8" endLine="83" endOffset="68"/></Target><Target id="@+id/progress_bar" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="86" startOffset="8" endLine="99" endOffset="71"/></Target><Target id="@+id/tv_loading" view="TextView"><Expressions/><location startLine="102" startOffset="8" endLine="114" endOffset="69"/></Target><Target id="@+id/video_loading_overlay" view="View"><Expressions/><location startLine="119" startOffset="4" endLine="128" endOffset="51"/></Target></Targets></Layout>