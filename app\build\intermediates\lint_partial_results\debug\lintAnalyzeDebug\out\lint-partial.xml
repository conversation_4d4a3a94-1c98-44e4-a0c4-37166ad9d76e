<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.bearmod.loader.test.KeyAuthTestActivity"
                    boolean="true"/>
                <entry
                    name="com.bearmod.loader.ui.splash.SplashActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.primary_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="21"
            column="12"
            startOffset="686"
            endLine="21"
            endColumn="32"
            endOffset="706"/>
        <location id="R.color.purple_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="162"
            endLine="5"
            endColumn="29"
            endOffset="179"/>
        <location id="R.color.teal_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="301"
            endLine="8"
            endColumn="27"
            endOffset="316"/>
        <location id="R.dimen.margin_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="2"
            column="16"
            startOffset="27"
            endLine="2"
            endColumn="36"
            endOffset="47"/>
        <location id="R.dimen.margin_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="3"
            column="16"
            startOffset="76"
            endLine="3"
            endColumn="35"
            endOffset="95"/>
        <location id="R.dimen.text_size_caption"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="7"
            column="16"
            startOffset="277"
            endLine="7"
            endColumn="40"
            endOffset="301"/>
        <location id="R.dimen.text_size_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="4"
            column="16"
            startOffset="123"
            endLine="4"
            endColumn="38"
            endOffset="145"/>
        <location id="R.dimen.text_size_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="5"
            column="16"
            startOffset="174"
            endLine="5"
            endColumn="39"
            endOffset="197"/>
        <location id="R.dimen.text_size_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="6"
            column="16"
            startOffset="226"
            endLine="6"
            endColumn="38"
            endOffset="248"/>
        <location id="R.drawable.app_logo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/app_logo.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="19"
            endColumn="10"
            endOffset="577"/>
        <location id="R.drawable.ic_check_circle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check_circle.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="429"/>
        <location id="R.drawable.ic_launcher_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="170"
            endColumn="10"
            endOffset="5605"/>
        <location id="R.drawable.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="1702"/>
        <location id="R.string.app_version_static"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="98"
            endLine="4"
            endColumn="38"
            endOffset="123"/>
        <location id="R.string.dark_mode"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="80"
            column="13"
            startOffset="4400"
            endLine="80"
            endColumn="29"
            endOffset="4416"/>
        <location id="R.string.download_complete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="63"
            column="13"
            startOffset="3419"
            endLine="63"
            endColumn="37"
            endOffset="3443"/>
        <location id="R.string.downloading"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="61"
            column="13"
            startOffset="3261"
            endLine="61"
            endColumn="31"
            endOffset="3279"/>
        <location id="R.string.info"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="101"
            column="13"
            startOffset="5563"
            endLine="101"
            endColumn="24"
            endOffset="5574"/>
        <location id="R.string.keyauth_init_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="1044"
            endLine="20"
            endColumn="39"
            endOffset="1070"/>
        <location id="R.string.language"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="81"
            column="13"
            startOffset="4448"
            endLine="81"
            endColumn="28"
            endOffset="4463"/>
        <location id="R.string.loading"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="97"
            column="13"
            startOffset="5390"
            endLine="97"
            endColumn="27"
            endOffset="5404"/>
        <location id="R.string.notification_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="78"
            column="13"
            startOffset="4278"
            endLine="78"
            endColumn="41"
            endOffset="4306"/>
        <location id="R.string.patch_status"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="1590"
            endLine="30"
            endColumn="32"
            endOffset="1609"/>
        <location id="R.string.retry"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="96"
            column="13"
            startOffset="5350"
            endLine="96"
            endColumn="25"
            endOffset="5362"/>
        <location id="R.string.select_target"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="27"
            column="13"
            startOffset="1428"
            endLine="27"
            endColumn="33"
            endOffset="1448"/>
        <location id="R.style.TextAppearance_BearLoader_Body1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="66"
            column="12"
            startOffset="3176"
            endLine="66"
            endColumn="50"
            endOffset="3214"/>
        <location id="R.style.TextAppearance_BearLoader_Body2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="72"
            column="12"
            startOffset="3476"
            endLine="72"
            endColumn="50"
            endOffset="3514"/>
        <location id="R.style.TextAppearance_BearLoader_Caption"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="77"
            column="12"
            startOffset="3726"
            endLine="77"
            endColumn="52"
            endOffset="3766"/>
        <location id="R.style.TextAppearance_BearLoader_Headline6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="59"
            column="12"
            startOffset="2861"
            endLine="59"
            endColumn="54"
            endOffset="2903"/>
        <location id="R.style.Theme_BearLoader_AppBarOverlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="84"
            column="12"
            startOffset="4030"
            endLine="84"
            endColumn="49"
            endOffset="4067"/>
        <location id="R.style.Theme_BearLoader_Dialog"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="36"
            column="12"
            startOffset="1715"
            endLine="36"
            endColumn="42"
            endOffset="1745"/>
        <location id="R.style.Theme_BearLoader_PopupOverlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="86"
            column="12"
            startOffset="4130"
            endLine="86"
            endColumn="48"
            endOffset="4166"/>
        <entry
            name="model"
            string="attr[actionBarSize(R),selectableItemBackground(R),colorPrimaryVariant(R)],color[success(U),white(U),primary(U),text_secondary(U),background(U),text_primary(U),card_background(U),progress_background(U),background_light(U),error(U),text_hint(U),ripple(U),divider(U),shimmer_color(U),purple_200(U),purple_500(D),purple_700(U),teal_200(U),teal_700(D),black(U),primary_dark(U),primary_light(D),accent(U),accent_dark(U),warning(U),info(U)],dimen[margin_medium(D),margin_small(D),text_size_large(D),text_size_medium(D),text_size_small(D),text_size_caption(D)],drawable[app_logo(D),bg_status(U),ic_app_icon(U),logo(U),ic_back(U),ic_check_circle(D),ic_clear_cache(U),ic_dashboard(U),ic_download(U),ic_empty(U),ic_error(U),ic_key(U),ic_launcher_background(D),ic_launcher_foreground(D),ic_launcher_foreground_1(E),ic_logout(U),ic_reset(U),ic_settings(U),logo_splash(U),splash_background(U)],id[toolbar(U),tv_available_releases(U),rv_releases(U),progress_loading(D),tv_no_releases(D),card_download_info(U),tv_apk_size(D),tv_obb_size(D),tv_total_size(D),btn_download(D),card_download_progress(D),tv_download_status(D),progress_download(D),tv_download_percentage(D),btn_cancel_download(D),animation_download_complete(D),iv_logo(U),tv_app_name(U),tv_login_subtitle(U),card_login(D),til_license_key(D),et_license_key(D),switch_remember(D),btn_login(D),tv_version(D),progress_login(D),progress_initializing(D),success_animation(D),view_overlay(D),drawer_layout(D),card_target_app(U),spinner_target_app(D),btn_scan_offsets(D),tv_available_patches(U),swipe_refresh(D),rv_patches(D),shimmer_layout(D),layout_empty(D),btn_download_patches(D),nav_view(D),tv_patch_name(U),card_mode(U),radio_group_mode(D),radio_root(D),radio_non_root(D),tv_logs_title(U),card_logs(D),btn_start_patching(U),tv_logs(D),progress_patching(D),layout_clear_cache(D),layout_reset_config(D),switch_stealth(D),switch_auto_login(D),btn_logout(D),tv_app_version(U),progress_bar(D),tv_status(U),tv_patch_description(U),tv_game_version(U),tv_update_date(D),btn_apply_patch(U),progressBar(U),shimmer_title(U),shimmer_status(D),shimmer_description(U),shimmer_version(U),shimmer_date(D),shimmer_button(D),card_release(U),tv_release_name(U),iv_download(U),tv_release_description(U),tv_release_date(U),ripple_effect(U),tv_license_info(U),nav_dashboard(U),nav_download(U),nav_settings(U),nav_keyauth_test(U),nav_logout(U)],layout[activity_download(U),item_release(U),activity_login(U),activity_main(U),item_patch(U),item_patch_shimmer(U),nav_header(U),activity_patch_execution(U),activity_settings(U),activity_splash(U)],menu[drawer_menu(U)],mipmap[ic_launcher(U),ic_launcher_round(U)],raw[download_complete(U),success_animation(U)],string[app_name(U),download_title(U),available_releases(U),no_releases_available(U),download_patches(U),download_progress(U),cancel_download(U),app_logo(U),login_subtitle(U),license_key(U),enter_license_key(U),remember_me(U),login(U),version_info(U),dashboard_title(U),appbar_scrolling_view_behavior(R),target_app(U),scan_offsets(U),available_patches(U),no_patches_available(U),patch_execution(U),execution_mode(U),root_mode(U),non_root_mode(U),execution_logs(U),start_patching(U),settings_title(U),app_settings(U),clear_cache(U),reset_config(U),patch_settings(U),toggle_stealth(U),auto_login(U),about(U),about_description(U),logout(U),app_version(U),apply_patch(U),license_valid_until(U),app_version_static(D),invalid_license_key_format(U),login_success(U),login_error(U),registration_date(U),days_remaining(U),keyauth_init_warning(U),keyauth_init_failed(D),select_target(D),patch_status(D),update_available(U),up_to_date(U),not_installed(U),game_version(U),updated(U),stop_patching(U),patching_in_progress(U),patching_complete(U),patching_failed(U),select_release_first(U),released(U),apk_size(U),obb_size(U),total_size(U),downloading(D),downloading_detailed(U),download_complete(D),download_complete_path(U),download_failed(U),download_cancelled(U),download_in_progress(U),continue_download(U),notification_settings(D),dark_mode(D),language(D),cache_cleared(U),config_reset(U),clear_cache_confirm(U),reset_config_confirm(U),logout_confirm(U),ok(U),cancel(U),retry(D),loading(D),error(U),success(U),warning(U),info(D),exit(U),continue_anyway(U),navigation_drawer_open(U),navigation_drawer_close(U),patches_updated(U),sync_error(U),scanning_offsets(U),scanning_offsets_for(U)],style[Theme_BearLoader(U),Theme_BearLoader_Splash(U),Theme_BearLoader_NoActionBar(U),Widget_BearLoader_Button(U),Widget_BearLoader_Button_Secondary(U),Widget_MaterialComponents_TextInputLayout_OutlinedBox(R),ThemeOverlay_AppCompat_Dark(R),TextAppearance_AppCompat_Body1(R),Theme_MaterialComponents_DayNight_DarkActionBar(R),Theme(R),Theme_BearLoader_Dialog(D),ThemeOverlay_MaterialComponents_Dialog_Alert(E),Widget_MaterialComponents_Button(R),Widget_MaterialComponents_Button_OutlinedButton(R),TextAppearance_BearLoader_Headline6(D),TextAppearance_MaterialComponents_Headline6(E),TextAppearance_BearLoader_Body1(D),TextAppearance_MaterialComponents_Body1(E),TextAppearance_BearLoader_Body2(D),TextAppearance_MaterialComponents_Body2(E),TextAppearance_BearLoader_Caption(D),TextAppearance_MaterialComponents_Caption(E),Theme_BearLoader_AppBarOverlay(D),ThemeOverlay_AppCompat_Dark_ActionBar(E),Theme_BearLoader_PopupOverlay(D),ThemeOverlay_AppCompat_Light(E)],xml[data_extraction_rules(U),backup_rules(U)];24^3,25^4^26,27^4,29^5,2a^5,2b^5,2c^6,30^31,32^5,33^5,34^5,35^26,36^7^26,88^7^5^0^27^98^4^99^8^37^38^89^9a^6^9^9b^f9^3c^9c^a^9d^fa^95,89^9^8^7e^9b^2b^5^6^7d^7f^1,8a^7^9e^35^97^8^47^9f^6^48^9^49^a0^a1^b^5^2d^c^d^2e^fb^a2^a3^e^f9^a4^96,8b^f8^5^0^a5^4^7^a6^9^a7^8^b^a8^f9^a9^55^58^8c^8d^aa^2c^6^9b^fa^8e^92,8c^9^8^70^6^5f^71^24^3^bc^72^f9^74,8d^9^10^76^78^79,8e^5^fc^9e^35^97^fd^bd,8f^7^5^0^27^ab^4^8^37^9^5f^ac^ad^ae^af^60^b^66^64^6^b0^f9,90^7^5^0^27^b1^4^37^b2^9^1^b3^29^8^f^b4^33^b5^b6^b7^b8^97^a4^6^b9^ba^f9,91^7^9e^35^97^8^47^bb^6^48^5^6e,92^2a^a5^2b^98^34^b1^32^ba,93^4^25,94^4^25,f6^fe^5^17^4^19^1a^16^2^7^8^6^11^13^14,f7^f8^36,f8^f6^ff,f9^102^4^5,fa^103^5,100^101^5^19^7^8,104^105^1f^8,106^107^20^8,108^109^21^6,10a^10b^22^6,10c^10d,10e^10f;;;"/>
    </map>

</incidents>
