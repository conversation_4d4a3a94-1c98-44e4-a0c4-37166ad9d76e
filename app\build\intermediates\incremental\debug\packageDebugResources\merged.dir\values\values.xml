<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#FF5722</color>
    <color name="accent_dark">#E64A19</color>
    <color name="background">#121212</color>
    <color name="background_light">#1E1E1E</color>
    <color name="black">#FF000000</color>
    <color name="card_background">#2D2D2D</color>
    <color name="divider">#1FFFFFFF</color>
    <color name="error">#F44336</color>
    <color name="info">#2196F3</color>
    <color name="primary">#2E7D32</color>
    <color name="primary_dark">#1B5E20</color>
    <color name="primary_light">#4CAF50</color>
    <color name="progress_background">#424242</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="ripple">#33FFFFFF</color>
    <color name="shimmer_color">#DDDDDD</color>
    <color name="success">#4CAF50</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_hint">#80FFFFFF</color>
    <color name="text_primary">#FFFFFF</color>
    <color name="text_secondary">#B3FFFFFF</color>
    <color name="warning">#FFC107</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="margin_medium">16dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="text_size_caption">12sp</dimen>
    <dimen name="text_size_large">18sp</dimen>
    <dimen name="text_size_medium">16sp</dimen>
    <dimen name="text_size_small">14sp</dimen>
    <string name="about">About</string>
    <string name="about_description">BearMod-Loader is a powerful tool for managing and applying patches to enhance your gaming experience.</string>
    <string name="apk_size">APK Size: %1$s</string>
    <string name="app_logo">App Logo</string>
    <string name="app_name">BearMod-Loader</string>
    <string name="app_settings">App Settings</string>
    <string name="app_version">Version 1.0</string>
    <string name="app_version_static">Version 1.0.0</string>
    <string name="apply_patch">Apply Patch</string>
    <string name="auto_login">Auto Login</string>
    <string name="available_patches">Available Patches</string>
    <string name="available_releases">Available Releases</string>
    <string name="cache_cleared">Cache cleared</string>
    <string name="cancel">Cancel</string>
    <string name="cancel_download">Cancel Download</string>
    <string name="clear_cache">Clear Cache</string>
    <string name="clear_cache_confirm">Are you sure you want to clear the cache? This will remove all downloaded patches.</string>
    <string name="config_reset">Configuration reset</string>
    <string name="continue_anyway">Continue Anyway</string>
    <string name="continue_download">Continue Download</string>
    <string name="dark_mode">Dark Mode</string>
    <string name="dashboard_title">Dashboard</string>
    <string name="days_remaining">%1$d days remaining</string>
    <string name="download_cancelled">Download Cancelled</string>
    <string name="download_complete">Download Complete</string>
    <string name="download_complete_path">Download Complete: %1$s</string>
    <string name="download_failed">Download Failed: %1$s</string>
    <string name="download_in_progress">Download in progress. Do you want to cancel?</string>
    <string name="download_patches">Download Patches</string>
    <string name="download_progress">Download Progress</string>
    <string name="download_title">Download</string>
    <string name="downloading">Downloading… %1$s / %2$s</string>
    <string name="downloading_detailed">Downloading… %1$s / %2$s • %3$s • ETA: %4$s</string>
    <string name="enter_license_key">Enter your license key</string>
    <string name="error">Error</string>
    <string name="execution_logs">Execution Logs</string>
    <string name="execution_mode">Execution Mode</string>
    <string name="exit">Exit</string>
    <string name="game_version">Game Version: %1$s</string>
    <string name="info">Information</string>
    <string name="invalid_license_key_format">Invalid license key format</string>
    <string name="keyauth_init_failed">Failed to initialize KeyAuth. Please check your internet connection and try again.</string>
    <string name="keyauth_init_warning">KeyAuth initialization warning. You may experience login issues.</string>
    <string name="language">Language</string>
    <string name="license_key">License Key</string>
    <string name="license_valid_until">License valid until: %1$s</string>
    <string name="loading">Loading…</string>
    <string name="login">Login</string>
    <string name="login_error">Login failed: %1$s</string>
    <string name="login_subtitle">Enter your license key to continue</string>
    <string name="login_success">Login successful</string>
    <string name="logout">Logout</string>
    <string name="logout_confirm">Are you sure you want to logout? You will need to enter your license key again.</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="no_patches_available">No patches available</string>
    <string name="no_releases_available">No releases available</string>
    <string name="non_root_mode">Non-Root Mode</string>
    <string name="not_installed">Not Installed</string>
    <string name="notification_settings">Notification Settings</string>
    <string name="obb_size">OBB Size: %1$s</string>
    <string name="ok">OK</string>
    <string name="patch_execution">Patch Execution</string>
    <string name="patch_settings">Patch Settings</string>
    <string name="patch_status">Patch Status</string>
    <string name="patches_updated">Patches updated</string>
    <string name="patching_complete">Patching complete</string>
    <string name="patching_failed">Patching failed: %1$s</string>
    <string name="patching_in_progress">Patching in progress…</string>
    <string name="registration_date">Registration date: %1$s</string>
    <string name="released">Released: %1$s</string>
    <string name="remember_me">Remember me</string>
    <string name="reset_config">Reset Configuration</string>
    <string name="reset_config_confirm">Are you sure you want to reset the configuration? This will restore all settings to their default values.</string>
    <string name="retry">Retry</string>
    <string name="root_mode">Root Mode</string>
    <string name="scan_offsets">Scan Offsets</string>
    <string name="scanning_offsets">Scanning Offsets</string>
    <string name="scanning_offsets_for">Scanning offsets for %1$s</string>
    <string name="select_release_first">Please select a release first</string>
    <string name="select_target">Select Target</string>
    <string name="settings_title">Settings</string>
    <string name="start_patching">Start Patching</string>
    <string name="stop_patching">Stop Patching</string>
    <string name="success">Success</string>
    <string name="sync_error">Sync error: %1$s</string>
    <string name="target_app">Target App</string>
    <string name="toggle_stealth">Toggle Stealth Mode</string>
    <string name="total_size">Total Size: %1$s</string>
    <string name="up_to_date">Up to Date</string>
    <string name="update_available">Update Available</string>
    <string name="updated">Updated: %1$s</string>
    <string name="version_info">Version %1$s</string>
    <string name="warning">Warning</string>
    <style name="TextAppearance.BearLoader.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textSize">@dimen/text_size_medium</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.BearLoader.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="android:textSize">@dimen/text_size_small</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>
    <style name="TextAppearance.BearLoader.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textSize">@dimen/text_size_caption</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>
    <style name="TextAppearance.BearLoader.Headline6" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="Theme.BearLoader" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/accent_dark</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
        <item name="android:windowBackground">@color/background</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
    </style>
    <style name="Theme.BearLoader.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="Theme.BearLoader.Dialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorAccent">@color/accent</item>
        <item name="android:background">@color/background</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
    </style>
    <style name="Theme.BearLoader.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>
    <style name="Theme.BearLoader.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="Theme.BearLoader.Splash" parent="Theme.BearLoader.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowTranslucentStatus">true</item>
    </style>
    <style name="Widget.BearLoader.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
    </style>
    <style name="Widget.BearLoader.Button.Secondary" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/primary</item>
        <item name="android:textColor">@color/primary</item>
        <item name="cornerRadius">8dp</item>
    </style>
</resources>