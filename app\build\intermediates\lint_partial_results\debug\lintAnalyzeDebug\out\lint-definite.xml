<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadActivity.java"
            line="165"
            column="26"
            startOffset="5930"
            endLine="165"
            endColumn="74"
            endOffset="5978"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/download/DownloadManager.java"
            line="154"
            column="73"
            startOffset="4915"
            endLine="154"
            endColumn="84"
            endOffset="4926"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="526"
            column="27"
            startOffset="20417"
            endLine="526"
            endColumn="38"
            endOffset="20428"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="548"
            column="27"
            startOffset="21167"
            endLine="548"
            endColumn="38"
            endOffset="21178"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/auth/LoginActivity.java"
            line="170"
            column="72"
            startOffset="5964"
            endLine="170"
            endColumn="83"
            endOffset="5975"/>
    </incident>

    <incident
        id="SimilarGradleDependency"
        severity="informational"
        message="There are multiple dependencies com.github.Tentoxa:KeyAuth-Java but with different version">
        <location
            file="../gradle/libs.versions.toml"
            line="50"
            column="17"
            startOffset="1563"
            endLine="50"
            endColumn="91"
            endOffset="1637"/>
    </incident>

    <incident
        id="SimilarGradleDependency"
        severity="informational"
        message="There are multiple dependencies com.github.Tentoxa:KeyAuth-Java but with different version">
        <location
            file="../gradle/libs.versions.toml"
            line="53"
            column="21"
            startOffset="1876"
            endLine="53"
            endColumn="98"
            endOffset="1953"/>
    </incident>

    <incident
        id="PluralsCandidate"
        severity="warning"
        message="Formatting %d followed by words (&quot;days&quot;): This should probably be a plural rather than a string">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="18"
            column="5"
            startOffset="859"
            endLine="18"
            endColumn="63"
            endOffset="917"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is never &lt; 24">
        <fix-data conditional="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/util/BuildValidator.java"
            line="48"
            column="13"
            startOffset="1533"
            endLine="48"
            endColumn="56"
            endOffset="1576"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `CloudSyncManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/cloud/CloudSyncManager.java"
            line="24"
            column="13"
            startOffset="571"
            endLine="24"
            endColumn="19"
            endOffset="577"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `DockerManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/patch/DockerManager.java"
            line="16"
            column="13"
            startOffset="330"
            endLine="16"
            endColumn="19"
            endOffset="336"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `DownloadManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/download/DownloadManager.java"
            line="34"
            column="13"
            startOffset="845"
            endLine="34"
            endColumn="19"
            endOffset="851"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `FridaManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/patch/FridaManager.java"
            line="16"
            column="13"
            startOffset="317"
            endLine="16"
            endColumn="19"
            endOffset="323"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `PatchManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/patch/PatchManager.java"
            line="20"
            column="13"
            startOffset="467"
            endLine="20"
            endColumn="19"
            endOffset="473"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="9"
            column="27"
            startOffset="300"
            endLine="9"
            endColumn="931"
            endOffset="1204"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="43"
            endOffset="379"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="43"
            endOffset="379"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_patch_execution.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="43"
            endOffset="379"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="43"
            endOffset="379"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/primary` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header.xml"
            line="5"
            column="5"
            startOffset="190"
            endLine="5"
            endColumn="40"
            endOffset="225"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/logo.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logo.png"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadActivity.java"
            line="179"
            column="50"
            startOffset="6494"
            endLine="179"
            endColumn="64"
            endOffset="6508"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/test/KeyAuthTestActivity.java"
            line="57"
            column="23"
            startOffset="1683"
            endLine="57"
            endColumn="47"
            endOffset="1707"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/test/KeyAuthTestActivity.java"
            line="64"
            column="30"
            startOffset="1930"
            endLine="64"
            endColumn="44"
            endOffset="1944"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/test/KeyAuthTestActivity.java"
            line="69"
            column="30"
            startOffset="2122"
            endLine="69"
            endColumn="66"
            endOffset="2158"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/test/KeyAuthTestActivity.java"
            line="74"
            column="31"
            startOffset="2325"
            endLine="74"
            endColumn="51"
            endOffset="2345"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/test/KeyAuthTestActivity.java"
            line="78"
            column="26"
            startOffset="2458"
            endLine="78"
            endColumn="33"
            endOffset="2465"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/test/KeyAuthTestActivity.java"
            line="82"
            column="29"
            startOffset="2579"
            endLine="82"
            endColumn="47"
            endOffset="2597"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/test/KeyAuthTestActivity.java"
            line="86"
            column="27"
            startOffset="2710"
            endLine="86"
            endColumn="35"
            endOffset="2718"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/test/KeyAuthTestActivity.java"
            line="91"
            column="29"
            startOffset="2870"
            endLine="91"
            endColumn="38"
            endOffset="2879"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/test/KeyAuthTestActivity.java"
            line="102"
            column="29"
            startOffset="3254"
            endLine="102"
            endColumn="38"
            endOffset="3263"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Debug&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/drawer_menu.xml"
            line="18"
            column="11"
            startOffset="659"
            endLine="18"
            endColumn="32"
            endOffset="680"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;KeyAuth Test&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/drawer_menu.xml"
            line="23"
            column="17"
            startOffset="835"
            endLine="23"
            endColumn="45"
            endOffset="863"/>
    </incident>

</incidents>
