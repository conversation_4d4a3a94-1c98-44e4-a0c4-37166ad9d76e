<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardBackgroundColor="@color/card_background"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <View
            android:id="@+id/shimmer_title"
            android:layout_width="200dp"
            android:layout_height="20dp"
            android:background="@color/shimmer_color"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/shimmer_status"
            android:layout_width="80dp"
            android:layout_height="20dp"
            android:background="@color/shimmer_color"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/shimmer_description"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginTop="8dp"
            android:background="@color/shimmer_color"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/shimmer_title" />

        <View
            android:id="@+id/shimmer_version"
            android:layout_width="100dp"
            android:layout_height="15dp"
            android:layout_marginTop="8dp"
            android:background="@color/shimmer_color"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/shimmer_description" />

        <View
            android:id="@+id/shimmer_date"
            android:layout_width="100dp"
            android:layout_height="15dp"
            android:layout_marginTop="8dp"
            android:background="@color/shimmer_color"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/shimmer_description" />

        <View
            android:id="@+id/shimmer_button"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginTop="16dp"
            android:background="@color/shimmer_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/shimmer_version" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
