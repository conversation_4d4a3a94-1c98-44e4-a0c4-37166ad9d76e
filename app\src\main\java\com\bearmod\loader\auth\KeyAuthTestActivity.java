package com.bearmod.loader.auth;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.bearmod.loader.BearLoaderApplication;
import com.bearmod.loader.R;
import com.google.android.material.textfield.TextInputEditText;

/**
 * Activity to test KeyAuth API connection
 */
public class KeyAuthTestActivity extends AppCompatActivity {

    private Button btnTest;
    private Button btnSetCustomDomain;
    private ProgressBar progressBar;
    private TextView tvResult;
    private TextView tvCustomDomainLabel;
    private TextInputEditText etCustomDomain;
    private DirectKeyAuthManager keyAuthManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_keyauth_test);

        // Get KeyAuth manager
        keyAuthManager = DirectKeyAuthManager.getInstance();

        // Initialize views
        btnTest = findViewById(R.id.btn_test);
        btnSetCustomDomain = findViewById(R.id.btn_set_custom_domain);
        progressBar = findViewById(R.id.progress_bar);
        tvResult = findViewById(R.id.tv_result);
        tvCustomDomainLabel = findViewById(R.id.tv_custom_domain_label);
        etCustomDomain = findViewById(R.id.et_custom_domain);

        // Set up test button
        btnTest.setOnClickListener(v -> runTest());

        // Set up custom domain button
        btnSetCustomDomain.setOnClickListener(v -> setCustomDomain());

        // Check if custom domain is already set
        updateCustomDomainDisplay();
    }

    /**
     * Update custom domain display
     */
    private void updateCustomDomainDisplay() {
        String customDomain = keyAuthManager.getCustomDomain();
        if (customDomain != null) {
            tvCustomDomainLabel.setText("Custom Domain: (Active)");
            etCustomDomain.setText(customDomain);
        } else {
            tvCustomDomainLabel.setText("Custom Domain:");
            etCustomDomain.setText("");
        }
    }

    /**
     * Set custom domain
     */
    private void setCustomDomain() {
        String domain = etCustomDomain.getText().toString().trim();

        if (TextUtils.isEmpty(domain)) {
            // Clear custom domain
            keyAuthManager.setCustomDomain(null);
            Toast.makeText(this, "Custom domain cleared", Toast.LENGTH_SHORT).show();
        } else {
            // Set custom domain
            keyAuthManager.setCustomDomain(domain);
            Toast.makeText(this, "Custom domain set: " + domain, Toast.LENGTH_SHORT).show();
        }

        // Update display
        updateCustomDomainDisplay();
    }

    /**
     * Run KeyAuth connection test
     */
    private void runTest() {
        // Show progress and disable button
        progressBar.setVisibility(View.VISIBLE);
        btnTest.setEnabled(false);
        tvResult.setText("Testing KeyAuth connection...");

        // Run test
        KeyAuthTest.testConnection(this, (success, message) -> {
            // Hide progress
            progressBar.setVisibility(View.GONE);
            btnTest.setEnabled(true);

            // Show result
            if (success) {
                tvResult.setText("✅ " + message);
            } else {
                tvResult.setText("❌ " + message);
                Toast.makeText(this, "Test failed: " + message, Toast.LENGTH_LONG).show();
            }
        });
    }
}
