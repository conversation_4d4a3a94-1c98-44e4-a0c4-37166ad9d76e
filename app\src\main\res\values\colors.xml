<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Original colors (keeping for reference) -->
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>

    <!-- Base colors -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- Status colors -->
    <color name="success">#4CAF50</color>
    <color name="error">#F44336</color>

    <!-- App theme colors -->
    <color name="primary">#2E7D32</color>
    <color name="primary_dark">#1B5E20</color>
    <color name="primary_light">#4CAF50</color>
    <color name="accent">#FF5722</color>
    <color name="accent_dark">#E64A19</color>
    <color name="accent_light">#FF8A65</color>

    <!-- Material Design 3 Surface colors -->
    <color name="surface_container_lowest">#0F0F0F</color>
    <color name="surface_container_low">#1A1A1A</color>
    <color name="surface_container">#1E1E1E</color>
    <color name="surface_container_high">#292929</color>
    <color name="surface_container_highest">#343434</color>

    <!-- Background colors -->
    <color name="background">#121212</color>
    <color name="background_light">#1E1E1E</color>
    <color name="background_overlay">#80121212</color>
    <color name="card_background">#2D2D2D</color>

    <!-- Text colors -->
    <color name="text_primary">#FFFFFF</color>
    <color name="text_secondary">#B3FFFFFF</color>
    <color name="text_hint">#80FFFFFF</color>

    <!-- Status colors -->
    <color name="warning">#FFC107</color>
    <color name="info">#2196F3</color>

    <!-- Specific UI element colors -->
    <color name="divider">#1FFFFFFF</color>
    <color name="ripple">#33FFFFFF</color>
    <color name="shimmer_color">#DDDDDD</color>
    <color name="progress_background">#424242</color>
</resources>