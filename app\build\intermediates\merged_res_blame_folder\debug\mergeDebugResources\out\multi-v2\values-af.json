{"logs": [{"outputFile": "com.bearmod.loader.app-mergeDebugResources-55:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95f2be59c03f86888596a581fb73e161\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3418,3516,3618,3716,3814,3921,4030,9924", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "3511,3613,3711,3809,3916,4025,4145,10020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\84e22dff9d3244eb5f30d696cec58c96\\transformed\\appcompat-1.7.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,432,528,634,719,822,940,1017,1093,1184,1277,1372,1466,1565,1658,1753,1852,1947,2041,2122,2229,2334,2431,2539,2642,2744,2898,9611", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "427,523,629,714,817,935,1012,1088,1179,1272,1367,1461,1560,1653,1748,1847,1942,2036,2117,2224,2329,2426,2534,2637,2739,2893,2991,9687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b19140129864dc831a67762e70345bb\\transformed\\navigation-ui-2.9.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,157", "endColumns": "101,116", "endOffsets": "152,269"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9315,9417", "endColumns": "101,116", "endOffsets": "9412,9529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a95a1075bc0403a1ca2712bac24841aa\\transformed\\material-1.12.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,513,608,696,796,910,991,1051,1115,1203,1269,1332,1418,1480,1541,1599,1665,1728,1783,1901,1958,2020,2075,2144,2263,2351,2426,2519,2604,2687,2826,2909,2990,3118,3205,3282,3340,3391,3457,3526,3602,3673,3749,3823,3902,3975,4046,4149,4236,4307,4396,4486,4558,4633,4720,4771,4850,4917,4998,5082,5144,5208,5271,5341,5445,5548,5644,5744,5806,5861,5938,6021,6097", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,77,94,87,99,113,80,59,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,74,92,84,82,138,82,80,127,86,76,57,50,65,68,75,70,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76,82,75,72", "endOffsets": "269,350,430,508,603,691,791,905,986,1046,1110,1198,1264,1327,1413,1475,1536,1594,1660,1723,1778,1896,1953,2015,2070,2139,2258,2346,2421,2514,2599,2682,2821,2904,2985,3113,3200,3277,3335,3386,3452,3521,3597,3668,3744,3818,3897,3970,4041,4144,4231,4302,4391,4481,4553,4628,4715,4766,4845,4912,4993,5077,5139,5203,5266,5336,5440,5543,5639,5739,5801,5856,5933,6016,6092,6165"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2996,3077,3157,3235,3330,4150,4250,4364,4445,4505,4569,4657,4723,4786,4872,4934,4995,5053,5119,5182,5237,5355,5412,5474,5529,5598,5717,5805,5880,5973,6058,6141,6280,6363,6444,6572,6659,6736,6794,6845,6911,6980,7056,7127,7203,7277,7356,7429,7500,7603,7690,7761,7850,7940,8012,8087,8174,8225,8304,8371,8452,8536,8598,8662,8725,8795,8899,9002,9098,9198,9260,9534,9692,9775,9851", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "endColumns": "12,80,79,77,94,87,99,113,80,59,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,74,92,84,82,138,82,80,127,86,76,57,50,65,68,75,70,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76,82,75,72", "endOffsets": "319,3072,3152,3230,3325,3413,4245,4359,4440,4500,4564,4652,4718,4781,4867,4929,4990,5048,5114,5177,5232,5350,5407,5469,5524,5593,5712,5800,5875,5968,6053,6136,6275,6358,6439,6567,6654,6731,6789,6840,6906,6975,7051,7122,7198,7272,7351,7424,7495,7598,7685,7756,7845,7935,8007,8082,8169,8220,8299,8366,8447,8531,8593,8657,8720,8790,8894,8997,9093,9193,9255,9310,9606,9770,9846,9919"}}]}]}