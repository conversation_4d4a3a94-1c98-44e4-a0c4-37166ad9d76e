D:\Augment_Code\BearLoader4\app\lint-baseline.xml: Hint: 47 warnings were filtered out because they are listed in the baseline file, lint-baseline.xml [LintBaseline]
D:\Augment_Code\BearLoader4\app\lint-baseline.xml: Hint: 58 errors/warnings were listed in the baseline file (lint-baseline.xml) but not found in the project; perhaps they have been fixed? Another possible explanation is that lint recently stopped analyzing (and including results from) dependent projects by default. You can turn this back on with android.lintOptions.checkDependencies=true. Unmatched issue types: ContentDescription, GradleDependency (2), HardcodedText (22), NotifyDataSetChanged, SetTextI18n (26), TrustAllX509TrustManager (2), UnusedResources, UseAppTint, UseTomlInstead (2) [LintBaselineFixed]

   Explanation for issues of type "LintBaselineFixed":
   If a lint baseline describes a problem which is no longer reported, then
   the problem has either been fixed, or perhaps the issue type has been
   disabled. In any case, the entry can be removed from the baseline (such
   that if the issue is reintroduced at some point, lint will complain rather
   than just silently starting to match the old baseline entry again.)

D:\Augment_Code\BearLoader4\app\src\main\java\com\bearmod\loader\auth\KeyAuthManager.java:526: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        message = message.toLowerCase();
                          ~~~~~~~~~~~
D:\Augment_Code\BearLoader4\app\src\main\java\com\bearmod\loader\auth\KeyAuthManager.java:548: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        message = message.toLowerCase();
                          ~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

D:\Augment_Code\BearLoader4\gradle\libs.versions.toml:50: Hint: There are multiple dependencies com.github.Tentoxa:KeyAuth-Java but with different version [SimilarGradleDependency]
keyauth-java = { module = "com.github.Tentoxa:KeyAuth-Java", version.ref = "keyauthJava" }
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearLoader4\gradle\libs.versions.toml:53: Hint: There are multiple dependencies com.github.Tentoxa:KeyAuth-Java but with different version [SimilarGradleDependency]
keyauth-java-api = { module = "com.github.Tentoxa:KeyAuth-Java", version.ref = "keyauthJavaApi" }
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SimilarGradleDependency":
   This detector looks for usages of libraries when name and group are the
   same but versions are different. Using multiple versions in big project is
   fine, and there are cases where you deliberately want to stick with such
   approach. However, you may simply not be aware that this situation happens,
   and that is what this lint check helps find.

D:\Augment_Code\BearLoader4\app\src\main\res\drawable\ic_check_circle.xml:2: Warning: The resource R.drawable.ic_check_circle appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearLoader4\app\src\main\res\values\strings.xml:20: Warning: The resource R.string.keyauth_init_failed appears to be unused [UnusedResources]
    <string name="keyauth_init_failed">Failed to initialize KeyAuth. Please check your internet connection and try again.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearLoader4\app\src\main\res\values\themes.xml:84: Warning: The resource R.style.Theme_BearLoader_AppBarOverlay appears to be unused [UnusedResources]
    <style name="Theme.BearLoader.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearLoader4\app\src\main\res\values\themes.xml:86: Warning: The resource R.style.Theme_BearLoader_PopupOverlay appears to be unused [UnusedResources]
    <style name="Theme.BearLoader.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

D:\Augment_Code\BearLoader4\app\src\main\java\com\bearmod\loader\test\KeyAuthTestActivity.java:57: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        title.setText("KeyAuth Test Interface");
                      ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearLoader4\app\src\main\java\com\bearmod\loader\test\KeyAuthTestActivity.java:64: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        labelLicense.setText("License Key:");
                             ~~~~~~~~~~~~~~
D:\Augment_Code\BearLoader4\app\src\main\java\com\bearmod\loader\test\KeyAuthTestActivity.java:69: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        etLicenseKey.setText("lEOEtm-OvCMIO-FgUWb4-wciL32-gzHm3g"); // Pre-fill with test key
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearLoader4\app\src\main\java\com\bearmod\loader\test\KeyAuthTestActivity.java:74: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        btnInitialize.setText("Initialize KeyAuth");
                              ~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearLoader4\app\src\main\java\com\bearmod\loader\test\KeyAuthTestActivity.java:78: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        btnLogin.setText("Login");
                         ~~~~~~~
D:\Augment_Code\BearLoader4\app\src\main\java\com\bearmod\loader\test\KeyAuthTestActivity.java:82: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        btnValidate.setText("Validate License");
                            ~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearLoader4\app\src\main\java\com\bearmod\loader\test\KeyAuthTestActivity.java:86: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        btnLogout.setText("Logout");
                          ~~~~~~~~
D:\Augment_Code\BearLoader4\app\src\main\java\com\bearmod\loader\test\KeyAuthTestActivity.java:91: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        labelStatus.setText("Status:");
                            ~~~~~~~~~
D:\Augment_Code\BearLoader4\app\src\main\java\com\bearmod\loader\test\KeyAuthTestActivity.java:102: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        labelResult.setText("Result:");
                            ~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

D:\Augment_Code\BearLoader4\app\src\main\res\menu\drawer_menu.xml:18: Warning: Hardcoded string "Debug", should use @string resource [HardcodedText]
    <item android:title="Debug">
          ~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearLoader4\app\src\main\res\menu\drawer_menu.xml:23: Warning: Hardcoded string "KeyAuth Test", should use @string resource [HardcodedText]
                android:title="KeyAuth Test" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

0 errors, 17 warnings, 2 hints (and 47 warnings filtered by baseline lint-baseline.xml)
