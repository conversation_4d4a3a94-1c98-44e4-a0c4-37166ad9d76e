<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 17 warnings and 2 hints</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Sun May 25 18:04:54 MMT 2025 by AGP (8.10.0)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#LintBaseline"><i class="material-icons warning-icon">warning</i>Baseline Applied (1)</a>
      <a class="mdl-navigation__link" href="#LintBaselineFixed"><i class="material-icons warning-icon">warning</i>Baselined Issues Fixed (1)</a>
      <a class="mdl-navigation__link" href="#DefaultLocale"><i class="material-icons warning-icon">warning</i>Implied default locale in case conversion (2)</a>
      <a class="mdl-navigation__link" href="#SimilarGradleDependency"><i class="material-icons warning-icon">warning</i>Multiple Versions Gradle Dependency (2)</a>
      <a class="mdl-navigation__link" href="#UnusedResources"><i class="material-icons warning-icon">warning</i>Unused resources (4)</a>
      <a class="mdl-navigation__link" href="#SetTextI18n"><i class="material-icons warning-icon">warning</i>TextView Internationalization (9)</a>
      <a class="mdl-navigation__link" href="#HardcodedText"><i class="material-icons warning-icon">warning</i>Hardcoded text (2)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Lint">Lint</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#LintBaseline">LintBaseline</a>: Baseline Applied</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#LintBaselineFixed">LintBaselineFixed</a>: Baselined Issues Fixed</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DefaultLocale">DefaultLocale</a>: Implied default locale in case conversion</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SimilarGradleDependency">SimilarGradleDependency</a>: Multiple Versions Gradle Dependency</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Performance">Performance</a>
</td></tr>
<tr>
<td class="countColumn">4</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedResources">UnusedResources</a>: Unused resources</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization">Internationalization</a>
</td></tr>
<tr>
<td class="countColumn">9</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SetTextI18n">SetTextI18n</a>: TextView Internationalization</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HardcodedText">HardcodedText</a>: Hardcoded text</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#ExtraIssues">Included Additional Checks (48)</a>
</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (39)</a>
</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Lint"></a>
<a name="LintBaseline"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="LintBaselineCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Baseline Applied</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../lint-baseline.xml">../../lint-baseline.xml</a></span>: <span class="message">47 warnings were filtered out because they are listed in the baseline file, lint-baseline.xml</span><br />
</div>
<div class="metadata"><div class="explanation" id="explanationLintBaseline" style="display: none;">
Lint can be configured with a "baseline"; a set of current issues found in a codebase, which future runs of lint will silently ignore. Only new issues not found in the baseline are reported.<br/>
<br/>
Note that while opening files in the IDE, baseline issues are not filtered out; the purpose of baselines is to allow you to get started using lint and break the build on all newly introduced errors, without having to go back and fix the entire codebase up front. However, when you open up existing files you still want to be aware of and fix issues as you come across them.<br/>
<br/>
This issue type is used to emit an informational-only warning if any issues were filtered out due to baseline matching. That way, you don't have a false sense of security if you forgot that you've checked in a baseline file.<br/>To suppress this error, use the issue id "LintBaseline" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">LintBaseline</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Hint</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 10/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationLintBaselineLink" onclick="reveal('explanationLintBaseline');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="LintBaselineCardLink" onclick="hideid('LintBaselineCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="LintBaselineFixed"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="LintBaselineFixedCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Baselined Issues Fixed</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../lint-baseline.xml">../../lint-baseline.xml</a></span>: <span class="message">58 errors/warnings were listed in the baseline file (lint-baseline.xml) but not found in the project; perhaps they have been fixed? Another possible explanation is that lint recently stopped analyzing (and including results from) dependent projects by default. You can turn this back on with <code>android.lintOptions.checkDependencies=true</code>. Unmatched issue types: ContentDescription, GradleDependency (2), HardcodedText (22), NotifyDataSetChanged, SetTextI18n (26), TrustAllX509TrustManager (2), UnusedResources, UseAppTint, UseTomlInstead (2)</span><br />
</div>
<div class="metadata"><div class="explanation" id="explanationLintBaselineFixed" style="display: none;">
If a lint baseline describes a problem which is no longer reported, then the problem has either been fixed, or perhaps the issue type has been disabled. In any case, the entry can be removed from the baseline (such that if the issue is reintroduced at some point, lint will complain rather than just silently starting to match the old baseline entry again.)<br/>To suppress this error, use the issue id "LintBaselineFixed" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">LintBaselineFixed</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Hint</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 10/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationLintBaselineFixedLink" onclick="reveal('explanationLintBaselineFixed');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="LintBaselineFixedCardLink" onclick="hideid('LintBaselineFixedCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="DefaultLocale"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DefaultLocaleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Implied default locale in case conversion</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bearmod/loader/auth/KeyAuthManager.java">../../src/main/java/com/bearmod/loader/auth/KeyAuthManager.java</a>:526</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>toLowerCase(Locale)</code> instead. For strings meant to be internal use <code>Locale.ROOT</code>, otherwise <code>Locale.getDefault()</code>.</span><br /><pre class="errorlines">
<span class="lineno"> 523 </span>        String message = e.getMessage();
<span class="lineno"> 524 </span>        <span class="keyword">if</span> (message == <span class="keyword">null</span>) <span class="keyword">return</span> <span class="keyword">false</span>;
<span class="lineno"> 525 </span>
<span class="caretline"><span class="lineno"> 526 </span>        message = message.<span class="warning">toLowerCase</span>();&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 527 </span>        <span class="keyword">return</span> message.contains(<span class="string">"network"</span>) ||
<span class="lineno"> 528 </span>               message.contains(<span class="string">"timeout"</span>) ||
<span class="lineno"> 529 </span>               message.contains(<span class="string">"connection"</span>) ||
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/auth/KeyAuthManager.java">../../src/main/java/com/bearmod/loader/auth/KeyAuthManager.java</a>:548</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>toLowerCase(Locale)</code> instead. For strings meant to be internal use <code>Locale.ROOT</code>, otherwise <code>Locale.getDefault()</code>.</span><br /><pre class="errorlines">
<span class="lineno"> 545 </span>            <span class="keyword">return</span> <span class="string">"An unknown error occurred. Please try again."</span>;
<span class="lineno"> 546 </span>        }
<span class="lineno"> 547 </span>
<span class="caretline"><span class="lineno"> 548 </span>        message = message.<span class="warning">toLowerCase</span>();&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 549 </span>
<span class="lineno"> 550 </span>        <span class="comment">// Network-related errors</span>
<span class="lineno"> 551 </span>        <span class="keyword">if</span> (message.contains(<span class="string">"network"</span>) || message.contains(<span class="string">"connection"</span>) ||
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationDefaultLocale" style="display: none;">
Calling <code>String#toLowerCase()</code> or <code>#toUpperCase()</code> <b>without specifying an explicit locale</b> is a common source of bugs. The reason for that is that those methods will use the current locale on the user's device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for <code>i</code> is <b>not</b> <code>I</code>.<br/>
<br/>
If you want the methods to just perform ASCII replacement, for example to convert an enum name, call <code>String#toUpperCase(Locale.ROOT)</code> instead. If you really want to use the current locale, call <code>String#toUpperCase(Locale.getDefault())</code> instead.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/util/Locale.html#default_locale">https://developer.android.com/reference/java/util/Locale.html#default_locale</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "DefaultLocale" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DefaultLocale</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDefaultLocaleLink" onclick="reveal('explanationDefaultLocale');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DefaultLocaleCardLink" onclick="hideid('DefaultLocaleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="SimilarGradleDependency"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SimilarGradleDependencyCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Multiple Versions Gradle Dependency</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:50</span>: <span class="message">There are multiple dependencies com.github.Tentoxa:KeyAuth-Java but with different version</span><br /><pre class="errorlines">
<span class="lineno">  47 </span>ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
<span class="lineno">  48 </span>espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
<span class="lineno">  49 </span>appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
<span class="caretline"><span class="lineno">  50 </span>keyauth-java = {<span class="warning"> module = "com.github.Tentoxa:KeyAuth-Java", version.ref = "keyauthJava" }</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  51 </span>material = { group = "com.google.android.material", name = "material", version.ref = "material" }
<span class="lineno">  52 </span>constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
<span class="lineno">  53 </span>keyauth-java-api = { module = "com.github.Tentoxa:KeyAuth-Java", version.ref = "keyauthJavaApi" }
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:53</span>: <span class="message">There are multiple dependencies com.github.Tentoxa:KeyAuth-Java but with different version</span><br /><pre class="errorlines">
<span class="lineno">  50 </span>keyauth-java = { module = "com.github.Tentoxa:KeyAuth-Java", version.ref = "keyauthJava" }
<span class="lineno">  51 </span>material = { group = "com.google.android.material", name = "material", version.ref = "material" }
<span class="lineno">  52 </span>constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
<span class="caretline"><span class="lineno">  53 </span>keyauth-java-api = {<span class="warning"> module = "com.github.Tentoxa:KeyAuth-Java", version.ref = "keyauthJavaApi" }</span>&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  54 </span>
<span class="lineno">  55 </span>
<span class="lineno">  56 </span># Navigation components
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSimilarGradleDependency" style="display: none;">
This detector looks for usages of libraries when name and group are the same but versions are different. Using multiple versions in big project is fine, and there are cases where you deliberately want to stick with such approach. However, you may simply not be aware that this situation happens, and that is what this lint check helps find.<br/>To suppress this error, use the issue id "SimilarGradleDependency" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SimilarGradleDependency</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Hint</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSimilarGradleDependencyLink" onclick="reveal('explanationSimilarGradleDependency');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SimilarGradleDependencyCardLink" onclick="hideid('SimilarGradleDependencyCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Performance"></a>
<a name="UnusedResources"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedResourcesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unused resources</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable/ic_check_circle.xml">../../src/main/res/drawable/ic_check_circle.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.ic_check_circle</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:20</span>: <span class="message">The resource <code>R.string.keyauth_init_failed</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  17 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"registration_date"</span>>Registration date: %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  18 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"days_remaining"</span>>%1$d days remaining<span class="tag">&lt;/string></span>
<span class="lineno">  19 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"keyauth_init_warning"</span>>KeyAuth initialization warning. You may experience login issues.<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  20 </span>  <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"keyauth_init_failed"</span></span>>Failed to initialize KeyAuth. Please check your internet connection and try again.<span class="tag">&lt;/string></span></span>
<span class="lineno">  21 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"version_info"</span>>Version %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  22 </span>
<span class="lineno">  23 </span>  <span class="comment">&lt;!-- Dashboard Screen --></span></pre>

<span class="location"><a href="../../src/main/res/values/themes.xml">../../src/main/res/values/themes.xml</a>:84</span>: <span class="message">The resource <code>R.style.Theme_BearLoader_AppBarOverlay</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 81 </span>  <span class="tag">&lt;/style></span>
<span class="lineno"> 82 </span>
<span class="lineno"> 83 </span>      <span class="comment">&lt;!-- Add this missing style --></span>
<span class="caretline"><span class="lineno"> 84 </span>  <span class="tag">&lt;style</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"Theme.BearLoader.AppBarOverlay"</span></span> <span class="attribute">parent</span>=<span class="value">"ThemeOverlay.AppCompat.Dark.ActionBar"</span> />&nbsp;&nbsp;</span>
<span class="lineno"> 85 </span>
<span class="lineno"> 86 </span>  <span class="tag">&lt;style</span><span class="attribute"> name</span>=<span class="value">"Theme.BearLoader.PopupOverlay"</span> <span class="attribute">parent</span>=<span class="value">"ThemeOverlay.AppCompat.Light"</span> />
</pre>

<span class="location"><a href="../../src/main/res/values/themes.xml">../../src/main/res/values/themes.xml</a>:86</span>: <span class="message">The resource <code>R.style.Theme_BearLoader_PopupOverlay</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 83 </span>      <span class="comment">&lt;!-- Add this missing style --></span>
<span class="lineno"> 84 </span>  <span class="tag">&lt;style</span><span class="attribute"> name</span>=<span class="value">"Theme.BearLoader.AppBarOverlay"</span> <span class="attribute">parent</span>=<span class="value">"ThemeOverlay.AppCompat.Dark.ActionBar"</span> />
<span class="lineno"> 85 </span>
<span class="caretline"><span class="lineno"> 86 </span>  <span class="tag">&lt;style</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"Theme.BearLoader.PopupOverlay"</span></span> <span class="attribute">parent</span>=<span class="value">"ThemeOverlay.AppCompat.Light"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 87 </span><span class="lineno"> 88 </span>
<span class="lineno"> 89 </span>  <span class="comment">&lt;!-- &lt;style name="Widget.BearLoader.Button.Secondary"
</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUnusedResources" style="display: none;">
Unused resources make applications larger and slow down builds.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
,<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>skip-libraries</b> (default is true):<br/>
Whether the unused resource check should skip reporting unused resources in libraries.<br/>
<br/>
Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with <code>checkDependencies=true</code>).<br/>
<br/>
However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnusedResources"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"skip-libraries"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedResources" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedResources</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedResourcesLink" onclick="reveal('explanationUnusedResources');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedResourcesCardLink" onclick="hideid('UnusedResourcesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization"></a>
<a name="SetTextI18n"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SetTextI18nCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">TextView Internationalization</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java">../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java</a>:57</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  54 </span>        
<span class="lineno">  55 </span>        <span class="comment">// Title</span>
<span class="lineno">  56 </span>        TextView title = <span class="keyword">new</span> TextView(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  57 </span>        title.setText(<span class="warning"><span class="string">"KeyAuth Test Interface"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  58 </span>        title.setTextSize(<span class="number">20</span>);
<span class="lineno">  59 </span>        title.setPadding(<span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">32</span>);
<span class="lineno">  60 </span>        layout.addView(title);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java">../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java</a>:64</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  61 </span>        
<span class="lineno">  62 </span>        <span class="comment">// License key input</span>
<span class="lineno">  63 </span>        TextView labelLicense = <span class="keyword">new</span> TextView(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  64 </span>        labelLicense.setText(<span class="warning"><span class="string">"License Key:"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  65 </span>        layout.addView(labelLicense);
<span class="lineno">  66 </span>        
<span class="lineno">  67 </span>        etLicenseKey = <span class="keyword">new</span> EditText(<span class="keyword">this</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java">../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java</a>:69</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  66 </span>     
<span class="lineno">  67 </span>     etLicenseKey = <span class="keyword">new</span> EditText(<span class="keyword">this</span>);
<span class="lineno">  68 </span>     etLicenseKey.setHint(<span class="string">"Enter license key here"</span>);
<span class="caretline"><span class="lineno">  69 </span>     etLicenseKey.setText(<span class="warning"><span class="string">"lEOEtm-OvCMIO-FgUWb4-wciL32-gzHm3g"</span></span>); <span class="comment">// Pre-fill with test key</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  70 </span>     layout.addView(etLicenseKey);
<span class="lineno">  71 </span>     
<span class="lineno">  72 </span>     <span class="comment">// Buttons</span></pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java">../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java</a>:74</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  71 </span>        
<span class="lineno">  72 </span>        <span class="comment">// Buttons</span>
<span class="lineno">  73 </span>        btnInitialize = <span class="keyword">new</span> Button(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  74 </span>        btnInitialize.setText(<span class="warning"><span class="string">"Initialize KeyAuth"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  75 </span>        layout.addView(btnInitialize);
<span class="lineno">  76 </span>        
<span class="lineno">  77 </span>        btnLogin = <span class="keyword">new</span> Button(<span class="keyword">this</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java">../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java</a>:78</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  75 </span>        layout.addView(btnInitialize);
<span class="lineno">  76 </span>        
<span class="lineno">  77 </span>        btnLogin = <span class="keyword">new</span> Button(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  78 </span>        btnLogin.setText(<span class="warning"><span class="string">"Login"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  79 </span>        layout.addView(btnLogin);
<span class="lineno">  80 </span>        
<span class="lineno">  81 </span>        btnValidate = <span class="keyword">new</span> Button(<span class="keyword">this</span>);
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="SetTextI18nDivLink" onclick="reveal('SetTextI18nDiv');" />+ 4 More Occurrences...</button>
<div id="SetTextI18nDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java">../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java</a>:82</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  79 </span>        layout.addView(btnLogin);
<span class="lineno">  80 </span>        
<span class="lineno">  81 </span>        btnValidate = <span class="keyword">new</span> Button(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  82 </span>        btnValidate.setText(<span class="warning"><span class="string">"Validate License"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  83 </span>        layout.addView(btnValidate);
<span class="lineno">  84 </span>        
<span class="lineno">  85 </span>        btnLogout = <span class="keyword">new</span> Button(<span class="keyword">this</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java">../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java</a>:86</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  83 </span>        layout.addView(btnValidate);
<span class="lineno">  84 </span>        
<span class="lineno">  85 </span>        btnLogout = <span class="keyword">new</span> Button(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  86 </span>        btnLogout.setText(<span class="warning"><span class="string">"Logout"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  87 </span>        layout.addView(btnLogout);
<span class="lineno">  88 </span>        
<span class="lineno">  89 </span>        <span class="comment">// Status display</span></pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java">../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java</a>:91</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  88 </span>        
<span class="lineno">  89 </span>        <span class="comment">// Status display</span>
<span class="lineno">  90 </span>        TextView labelStatus = <span class="keyword">new</span> TextView(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  91 </span>        labelStatus.setText(<span class="warning"><span class="string">"Status:"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  92 </span>        labelStatus.setPadding(<span class="number">0</span>, <span class="number">32</span>, <span class="number">0</span>, <span class="number">8</span>);
<span class="lineno">  93 </span>        layout.addView(labelStatus);
<span class="lineno">  94 </span>        
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java">../../src/main/java/com/bearmod/loader/test/KeyAuthTestActivity.java</a>:102</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  99 </span>        
<span class="lineno"> 100 </span>        <span class="comment">// Result display</span>
<span class="lineno"> 101 </span>        TextView labelResult = <span class="keyword">new</span> TextView(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno"> 102 </span>        labelResult.setText(<span class="warning"><span class="string">"Result:"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 103 </span>        labelResult.setPadding(<span class="number">0</span>, <span class="number">16</span>, <span class="number">0</span>, <span class="number">8</span>);
<span class="lineno"> 104 </span>        layout.addView(labelResult);
<span class="lineno"> 105 </span>        
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationSetTextI18n" style="display: none;">
When calling <code>TextView#setText</code><br/>
* Never call <code>Number#toString()</code> to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using <code>String#format</code> with proper format specifications (<code>%d</code> or <code>%f</code>) instead.<br/>
* Do not pass a string literal (e.g. "Hello") to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.<br/>
* Do not build messages by concatenating text chunks. Such messages can not be properly translated.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/localization.html">https://developer.android.com/guide/topics/resources/localization.html</a>
</div>To suppress this error, use the issue id "SetTextI18n" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SetTextI18n</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSetTextI18nLink" onclick="reveal('explanationSetTextI18n');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SetTextI18nCardLink" onclick="hideid('SetTextI18nCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="HardcodedText"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HardcodedTextCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Hardcoded text</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/menu/drawer_menu.xml">../../src/main/res/menu/drawer_menu.xml</a>:18</span>: <span class="message">Hardcoded string "Debug", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 15 </span>            <span class="prefix">android:</span><span class="attribute">title</span>=<span class="value">"@string/settings_title"</span> />
<span class="lineno"> 16 </span>    <span class="tag">&lt;/group></span>
<span class="lineno"> 17 </span>
<span class="caretline"><span class="lineno"> 18 </span>    <span class="tag">&lt;item</span><span class="attribute"> </span><span class="warning"><span class="prefix">android:</span><span class="attribute">title</span>=<span class="value">"Debug"</span></span>>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 19 </span>        <span class="tag">&lt;menu></span>
<span class="lineno"> 20 </span>            <span class="tag">&lt;item</span><span class="attribute">
</span><span class="lineno"> 21 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/nav_keyauth_test"</span></pre>

<span class="location"><a href="../../src/main/res/menu/drawer_menu.xml">../../src/main/res/menu/drawer_menu.xml</a>:23</span>: <span class="message">Hardcoded string "KeyAuth Test", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 20 </span>            <span class="tag">&lt;item</span><span class="attribute">
</span><span class="lineno"> 21 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/nav_keyauth_test"</span>
<span class="lineno"> 22 </span>                <span class="prefix">android:</span><span class="attribute">icon</span>=<span class="value">"@drawable/ic_settings"</span>
<span class="caretline"><span class="lineno"> 23 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">title</span>=<span class="value">"KeyAuth Test"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 24 </span>        <span class="tag">&lt;/menu></span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;/item></span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationHardcodedText" style="display: none;">
Hardcoding text attributes directly in layout files is bad for several reasons:<br/>
<br/>
* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)<br/>
<br/>
* The application cannot be translated to other languages by just adding new translations for existing string resources.<br/>
<br/>
There are quickfixes to automatically extract this hardcoded string into a resource lookup.<br/>To suppress this error, use the issue id "HardcodedText" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HardcodedText</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHardcodedTextLink" onclick="reveal('explanationHardcodedText');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HardcodedTextCardLink" onclick="hideid('HardcodedTextCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="ExtraIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ExtraIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Included Additional Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
This card lists all the extra checks run by lint, provided from libraries,
build configuration and extra flags. This is included to help you verify
whether a particular check is included in analysis when configuring builds.
(Note that the list does not include the hundreds of built-in checks into lint,
only additional ones.)
<div id="IncludedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">BadConfigurationProvider<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
An <code>android.app.Application</code> must implement <code>androidx.work.Configuration.Provider</code><br/>
for on-demand initialization.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BadPeriodicWorkRequestEnqueue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using <code>enqueue()</code> for `PeriodicWorkRequest`s, you might end up enqueuing<br/>
duplicate requests unintentionally. You should be using<br/>
<code>enqueueUniquePeriodicWork</code> with an <code>ExistingPeriodicWorkPolicy.KEEP</code> instead.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DeepLinkInActivityDestination<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Attaching a &lt;deeplink> to an &lt;activity> destination will never give                 the right behavior when using an implicit deep link on another app's task                 (where the system back should immediately take the user back to the app that                 triggered the deep link). Instead, attach the deep link directly to                 the second activity (either by manually writing the appropriate &lt;intent-filter>                 or by adding the &lt;deeplink> to the start destination of a nav host in that second                 activity).<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DetachAndAttachSameFragment<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When doing a FragmentTransaction that includes both attach()                 and detach() operations being committed on the same fragment instance, it is a                 no-op. The reason for this is that the FragmentManager optimizes all operations                 within a single transaction so the attach() and detach() cancel each other out                 and neither is actually executed. To get the desired behavior, you should separate                 the attach() and detach() calls into separate FragmentTransactions.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DialogFragmentCallbacksDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using a <code>DialogFragment</code>, the <code>setOnCancelListener</code> and                 <code>setOnDismissListener</code> callback functions within the <code>onCreateDialog</code> function                  __must not be used__ because the <code>DialogFragment</code> owns these callbacks.                  Instead the respective <code>onCancel</code> and <code>onDismiss</code> functions can be used to                  achieve the desired effect.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EmptyNavDeepLink<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Attempting to create an empty NavDeepLink will result in an IllegalStateException at runtime. You may set these arguments within the lambda of the call to navDeepLink.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.common<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerMetadata<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a library defines a Initializer, it needs to be accompanied by a corresponding &lt;meta-data> entry in the AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerNoArgConstr<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Every <code>Initializer</code> must have a no argument constructor.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExperimentalAnnotationRetention<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental annotations defined in Java source should use default (<code>CLASS</code>) retention, while Kotlin-sourced annotations should use <code>BINARY</code> retention.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentAddMenuProvider<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentBackPressedCallback<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentLiveDataObserve<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When observing a LiveData object from a fragment's onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment's view is active.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentTagUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
FragmentContainerView replaces the &lt;fragment> tag as the preferred                 way of adding fragments via XML. Unlike the &lt;fragment> tag, FragmentContainerView                 uses a normal <code>FragmentTransaction</code> under the hood to add the initial fragment,                 allowing further FragmentTransaction operations on the FragmentContainerView                 and providing a consistent timing for lifecycle events.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html">https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IdleBatteryChargingConstraints<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Some devices are never considered charging and idle at the same time.<br/>
Consider removing one of these constraints.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidFragmentVersionForActivityResult<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In order to use the ActivityResult APIs you must upgrade your                 Fragment version to 1.3.0. Previous versions of FragmentActivity                 failed to call super.onRequestPermissionsResult() and used invalid request codes<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/permissions/requesting#make-the-request">https://developer.android.com/training/permissions/requesting#make-the-request</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.activity<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=527362">https://issuetracker.google.com/issues/new?component=527362</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPeriodicWorkRequestInterval<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The interval duration for a <code>PeriodicWorkRequest</code> must be at least 15 minutes.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidSetHasFixedSize<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a RecyclerView uses <code>setHasFixedSize(...)</code> you cannot use <code>wrap_content</code> for  size in the scrolling direction.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.recyclerview<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460887">https://issuetracker.google.com/issues/new?component=460887</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingKeepAnnotation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Type-safe nav arguments such as Enum types can get incorrectly obfuscated in minified builds when not referenced directly<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingKeepAnnotation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Type-safe nav arguments such as Enum types can get incorrectly obfuscated in minified builds when not referenced directly<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.common<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingSerializableAnnotation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The destination needs to be annotated with @Serializable in order for Navigation library to convert the class or object declaration into a NavDestination.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingSerializableAnnotation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The destination needs to be annotated with @Serializable in order for Navigation library to convert the class or object declaration into a NavDestination.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.common<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NullSafeMutableLiveData<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This check ensures that LiveData values are not null when explicitly                 declared as non-nullable.<br/>
<br/>
                Kotlin interoperability does not support enforcing explicit null-safety when using                 generic Java type parameters. Since LiveData is a Java class its value can always                 be null even when its type is explicitly declared as non-nullable. This can lead                 to runtime exceptions from reading a null LiveData value that is assumed to be                 non-nullable.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RemoveWorkManagerInitializer<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If an <code>android.app.Application</code> implements <code>androidx.work.Configuration.Provider</code>,<br/>
the default <code>androidx.startup.InitializationProvider</code> needs to be removed from the<br/>
AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RepeatOnLifecycleWrongUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used when the View is created,                 that is in the <code>onCreate</code> lifecycle method for Activities, or <code>onViewCreated</code> in                 case you're using Fragments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SpecifyForegroundServiceType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using the setForegroundAsync() API, the application must override &lt;service /> entry for <code>SystemForegroundService</code> to include the foreground service type in the  <code>AndroidManifest.xml</code> file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SpecifyJobSchedulerIdRange<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using <code>JobScheduler</code> APIs directly, <code>WorkManager</code> requires that developers specify a range of <code>JobScheduler</code> ids that are safe for <code>WorkManager</code> to use so the `id`s do not collide. <br/>
For more information look at <code>androidx.work.Configuration.Builder.setJobSchedulerJobIdRange(int, int)</code>.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeLifecycleWhenUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the <code>Lifecycle</code> is destroyed within the block of                     <code>Lifecycle.whenStarted</code> or any similar <code>Lifecycle.when</code> method is suspended,                     the block will be cancelled, which will also cancel any child coroutine                     launched inside the block. As as a result, If you have a try finally block                     in your code, the finally might run after the Lifecycle moves outside                     the desired state. It is recommended to check the <code>Lifecycle.isAtLeast</code>                     before accessing UI in finally block. Similarly,                     if you have a catch statement that might catch <code>CancellationException</code>,                     you should check the <code>Lifecycle.isAtLeast</code> before accessing the UI. See                     documentation of <code>Lifecycle.whenStateAtLeast</code> for more details<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageError<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with error-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageError</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageError">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageWarning<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with warning-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageWarning</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageWarning">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeRepeatOnLifecycleDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used with the viewLifecycleOwner                 in Fragments as opposed to lifecycleOwner.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAndroidAlpha<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ColorStateList</code> uses app:alpha without <code>android:alpha</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAppTint<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ImageView</code> or <code>ImageButton</code> uses <code>android:tint</code> instead of <code>app:tint</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForColorStateLists<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of color state lists<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForDrawables<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableApis<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of compound text view drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>TextView</code> uses <code>android:</code> compound drawable attributes instead of <code>app:</code> ones<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseGetLayoutInflater<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Using LayoutInflater.from(Context) can return a LayoutInflater                  that does not have the correct theme.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseRequireInsteadOfGet<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
AndroidX added new "require____()" versions of common "get___()" APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseRxSetProgress2<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>setCompletableProgress(...)</code> instead of `setProgress(...) in <code>RxWorker</code>.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSupportActionBar<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>AppCompatActivity.setSupportActionBar</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialCode<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>MaterialSwitch</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>MaterialSwitch</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsingOnClickInXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Old versions of the platform do not properly support resolving <code>android:onClick</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WorkerHasAPublicModifier<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When you define a ListenableWorker which is constructed using the <br/>
default WorkerFactory, the ListenableWorker sub-type needs to be public.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongNavigateRouteType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the destination class contains arguments, the route is expected to be class instance with the arguments filled in.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongRequiresOptIn<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental features defined in Kotlin source code must be annotated with the Kotlin<br/>
<code>@RequiresOptIn</code> annotation. Using <code>androidx.annotation.RequiresOptIn</code> will prevent the<br/>
Kotlin compiler from enforcing its opt-in policies.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongStartDestinationType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the startDestination contains arguments, the arguments must be provided to navigation via a fully formed route (a class instance with argumentsfilled in), or else it will be treated as a case of missing arguments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongStartDestinationType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the startDestination contains arguments, the arguments must be provided to navigation via a fully formed route (a class instance with argumentsfilled in), or else it will be treated as a case of missing arguments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.common<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IncludedIssuesLink" onclick="reveal('IncludedIssues');">
List Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ExtraIssuesCardLink" onclick="hideid('ExtraIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerify<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/design">https://d.android.com/r/studio-ui/designer/material/design</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Previously, launcher icons were required to be in the PNG format but that restriction is no longer there, so lint now flags these.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultEncoding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Some APIs will implicitly use the default system character encoding instead of UTF-8 when converting to or from bytes, such as when creating a default <code>FileReader</code>.<br/>
<br/>
This is <i>usually</i> not correct; you only want to do this if you need to read files created by other programs where they have deliberately written in the same encoding. The default encoding varies from platform to platform and can vary from locale to locale, so this makes it difficult to interpret files containing non-ASCII characters.<br/>
<br/>
We recommend using UTF-8 everywhere.<br/>
<br/>
Note that on Android, the default file encoding is always UTF-8 (see <a href="https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(">https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(</a>) for more), so this lint check deliberately does not flag any problems in Android code, since it is always safe to rely on the default character encoding there.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a <code>get</code> prefix or for boolean-returning methods an <code>is</code> prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlincFE10<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
K2, the new version of Kotlin compiler, which encompasses the new frontend, is coming. Try to avoid using internal APIs from the old frontend if possible.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin, parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintDocExample<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Lint's tool for generating documentation for each issue has special support for including a code example which shows how to trigger the report. It will pick the first unit test it can find and pick out the source file referenced from the error message, but you can instead designate a unit test to be the documentation example, and in that case, all the files are included.<br/>
<br/>
To designate a unit test as the documentation example for an issue, name the test <code>testDocumentationExample</code>, or if your detector reports multiple issues, <code>testDocumentationExample</code>&lt;Id>, such as <code>testDocumentationExampleMyId</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>; use <code>PsiEquivalenceUtil.areElementsEquivalent(PsiElement, PsiElement)</code> instead.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewerVersionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields. These require the use of backticks to escape when calling from Kotlin. Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, ActionEvent's <code>getWhen()</code> method requires backticks when used from Kotlin:
<pre>
val timestamp = event.`when`
</pre>
<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoOp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check looks for code which looks like it's a no-op -- usually leftover expressions from interactive debugging, but in some cases bugs where you had intended to do something with the expression such as assign it to a field.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>pure-getters</b> (default is false):<br/>
Whether to assume methods with getter-names have no side effects.<br/>
<br/>
Getter methods (where names start with <code>get</code> or <code>is</code>, and have non-void return types, and no arguments) should not have side effects. With this option turned on, lint will assume that is the case and will list any getter calls whose results are ignored as suspicious code.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"NoOp"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"pure-getters"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionNamingConvention<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Permissions should be prefixed with an app's package name, using reverse-domain-style naming. This prefix should be followed by <code>.permission.</code>, and then a description of the capability that the permission represents, in upper SNAKE_CASE. For example, <code>com.example.myapp.permission.ENGAGE_HYPERSPACE</code>.<br/>
<br/>
Following this recommendation avoids naming collisions, and helps clearly identify the owner and intention of a custom permission.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PrivacySandboxBlockedCall<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Many APIs are unavailable in the Privacy Sandbox, depending on the <code>targetSdk</code>.<br/>
<br/>
If your code is designed to run in the sandbox (and never outside the sandbox) then you should remove the blocked calls to avoid exceptions at runtime.<br/>
<br/>
If your code is part of a library that can be executed both inside and outside the sandbox, surround the code with <code>if (!Process.isSdkSandbox()) { ... }</code> (or use your own field or method annotated with <code>@ChecksRestrictedEnvironment</code>) to avoid executing blocked calls when in the sandbox. Or, add the <code>@RestrictedForEnvironment</code> annotation to the containing method if the entire method should not be called when in the sandbox.<br/>
<br/>
This check is disabled by default, and should only be enabled in modules that may execute in the Privacy Sandbox.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatTrivial<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Every call to <code>String.format</code> creates a new <code>Formatter</code> instance, which will decrease the performance of your app. <code>String.format</code> should only be used when necessary--if the formatted string contains only trivial conversions (e.g. <code>b</code>, <code>s</code>, <code>c</code>) and there are no translation concerns, it will be more efficient to replace them and concatenate with <code>+</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). Use the right single quotation mark for apostrophes. Never use generic quotes ", ' or free-standing accents `, ´ for quotation marks, apostrophes, or primes. This can make the text more readable.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>ignore-deprecated</b> (default is false):<br/>
Whether to ignore classes and members that have been annotated with <code>@Deprecated</code>.<br/>
<br/>
Normally this lint check will flag all unannotated elements, but by setting this option to <code>true</code> it will skip any deprecated elements.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnknownNullness"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"ignore-deprecated"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/interop#nullability_annotations">https://developer.android.com/kotlin/interop#nullability_annotations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported large screen hardware feature. Any &lt;uses-feature> not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a ChromeOS, large screen, or foldable device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>