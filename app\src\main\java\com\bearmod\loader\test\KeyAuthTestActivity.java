package com.bearmod.loader.test;

import android.app.Activity;
import android.os.Bundle;
import android.util.Log;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.bearmod.loader.R;
import com.bearmod.loader.auth.AuthResult;
import com.bearmod.loader.auth.KeyAuthManager;

/**
 * Test activity for KeyAuth integration
 * This activity allows testing the KeyAuth implementation with real license keys
 */
public class KeyAuthTestActivity extends Activity {

    private static final String TAG = "KeyAuthTestActivity";
    
    private EditText etLicenseKey;
    private Button btnInitialize;
    private Button btnLogin;
    private Button btnValidate;
    private Button btnLogout;
    private TextView tvStatus;
    private TextView tvResult;
    
    private KeyAuthManager keyAuthManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Create simple layout programmatically
        createLayout();
        
        // Initialize KeyAuth manager
        keyAuthManager = KeyAuthManager.getInstance();
        
        // Set up button listeners
        setupButtonListeners();
        
        updateStatus("Ready for testing");
    }
    
    private void createLayout() {
        // Create a simple vertical layout
        android.widget.LinearLayout layout = new android.widget.LinearLayout(this);
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(32, 32, 32, 32);
        
        // Title
        TextView title = new TextView(this);
        title.setText("KeyAuth Test Interface");
        title.setTextSize(20);
        title.setPadding(0, 0, 0, 32);
        layout.addView(title);
        
        // License key input
        TextView labelLicense = new TextView(this);
        labelLicense.setText("License Key:");
        layout.addView(labelLicense);
        
        etLicenseKey = new EditText(this);
        etLicenseKey.setHint("Enter license key here");
        etLicenseKey.setText("lEOEtm-OvCMIO-FgUWb4-wciL32-gzHm3g"); // Pre-fill with test key
        layout.addView(etLicenseKey);
        
        // Buttons
        btnInitialize = new Button(this);
        btnInitialize.setText("Initialize KeyAuth");
        layout.addView(btnInitialize);
        
        btnLogin = new Button(this);
        btnLogin.setText("Login");
        layout.addView(btnLogin);
        
        btnValidate = new Button(this);
        btnValidate.setText("Validate License");
        layout.addView(btnValidate);
        
        btnLogout = new Button(this);
        btnLogout.setText("Logout");
        layout.addView(btnLogout);
        
        // Status display
        TextView labelStatus = new TextView(this);
        labelStatus.setText("Status:");
        labelStatus.setPadding(0, 32, 0, 8);
        layout.addView(labelStatus);
        
        tvStatus = new TextView(this);
        tvStatus.setBackgroundColor(0xFFE0E0E0);
        tvStatus.setPadding(16, 16, 16, 16);
        layout.addView(tvStatus);
        
        // Result display
        TextView labelResult = new TextView(this);
        labelResult.setText("Result:");
        labelResult.setPadding(0, 16, 0, 8);
        layout.addView(labelResult);
        
        tvResult = new TextView(this);
        tvResult.setBackgroundColor(0xFFE0E0E0);
        tvResult.setPadding(16, 16, 16, 16);
        tvResult.setMinHeight(200);
        layout.addView(tvResult);
        
        setContentView(layout);
    }
    
    private void setupButtonListeners() {
        btnInitialize.setOnClickListener(v -> testInitialization());
        btnLogin.setOnClickListener(v -> testLogin());
        btnValidate.setOnClickListener(v -> testValidation());
        btnLogout.setOnClickListener(v -> testLogout());
    }
    
    private void testInitialization() {
        updateStatus("Initializing KeyAuth...");
        Log.d(TAG, "Starting KeyAuth initialization test");
        
        keyAuthManager.initialize(this, new KeyAuthManager.AuthCallback() {
            @Override
            public void onSuccess(AuthResult result) {
                Log.d(TAG, "Initialization successful: " + result.getMessage());
                updateStatus("✅ Initialization successful");
                updateResult("Success: " + result.getMessage());
                Toast.makeText(KeyAuthTestActivity.this, "KeyAuth initialized successfully", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Initialization failed: " + error);
                updateStatus("❌ Initialization failed");
                updateResult("Error: " + error);
                Toast.makeText(KeyAuthTestActivity.this, "Initialization failed: " + error, Toast.LENGTH_LONG).show();
            }
        });
    }
    
    private void testLogin() {
        String licenseKey = etLicenseKey.getText().toString().trim();
        
        if (licenseKey.isEmpty()) {
            Toast.makeText(this, "Please enter a license key", Toast.LENGTH_SHORT).show();
            return;
        }
        
        updateStatus("Logging in with license key...");
        Log.d(TAG, "Starting login test with license key: " + licenseKey.substring(0, Math.min(8, licenseKey.length())) + "...");
        
        keyAuthManager.login(licenseKey, new KeyAuthManager.AuthCallback() {
            @Override
            public void onSuccess(AuthResult result) {
                Log.d(TAG, "Login successful");
                updateStatus("✅ Login successful");
                
                StringBuilder resultText = new StringBuilder();
                resultText.append("Login Success!\n\n");
                resultText.append("Message: ").append(result.getMessage()).append("\n");
                
                if (result.getExpiryDate() != null) {
                    resultText.append("Expiry Date: ").append(keyAuthManager.formatExpiryDate(result.getExpiryDate())).append("\n");
                    resultText.append("Days Remaining: ").append(keyAuthManager.getRemainingDays(result.getExpiryDate())).append("\n");
                }
                
                if (result.getRegistrationDate() != null) {
                    resultText.append("Registration Date: ").append(result.getRegistrationDate()).append("\n");
                }
                
                updateResult(resultText.toString());
                Toast.makeText(KeyAuthTestActivity.this, "Login successful!", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Login failed: " + error);
                updateStatus("❌ Login failed");
                updateResult("Login Error: " + error);
                Toast.makeText(KeyAuthTestActivity.this, "Login failed: " + error, Toast.LENGTH_LONG).show();
            }
        });
    }
    
    private void testValidation() {
        updateStatus("Validating license...");
        Log.d(TAG, "Starting license validation test");
        
        keyAuthManager.validateLicense(new KeyAuthManager.AuthCallback() {
            @Override
            public void onSuccess(AuthResult result) {
                Log.d(TAG, "Validation successful");
                updateStatus("✅ License valid");
                
                StringBuilder resultText = new StringBuilder();
                resultText.append("Validation Success!\n\n");
                resultText.append("Message: ").append(result.getMessage()).append("\n");
                
                if (result.getExpiryDate() != null) {
                    resultText.append("Expiry Date: ").append(keyAuthManager.formatExpiryDate(result.getExpiryDate())).append("\n");
                    resultText.append("Days Remaining: ").append(keyAuthManager.getRemainingDays(result.getExpiryDate())).append("\n");
                }
                
                updateResult(resultText.toString());
                Toast.makeText(KeyAuthTestActivity.this, "License is valid!", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Validation failed: " + error);
                updateStatus("❌ License invalid");
                updateResult("Validation Error: " + error);
                Toast.makeText(KeyAuthTestActivity.this, "Validation failed: " + error, Toast.LENGTH_LONG).show();
            }
        });
    }
    
    private void testLogout() {
        updateStatus("Logging out...");
        Log.d(TAG, "Starting logout test");
        
        try {
            keyAuthManager.logout();
            updateStatus("✅ Logged out");
            updateResult("Logout successful");
            Toast.makeText(this, "Logged out successfully", Toast.LENGTH_SHORT).show();
            Log.d(TAG, "Logout successful");
        } catch (Exception e) {
            updateStatus("❌ Logout failed");
            updateResult("Logout Error: " + e.getMessage());
            Toast.makeText(this, "Logout failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
            Log.e(TAG, "Logout failed", e);
        }
    }
    
    private void updateStatus(String status) {
        runOnUiThread(() -> {
            tvStatus.setText(status);
            Log.d(TAG, "Status: " + status);
        });
    }
    
    private void updateResult(String result) {
        runOnUiThread(() -> {
            tvResult.setText(result);
            Log.d(TAG, "Result: " + result);
        });
    }
}
