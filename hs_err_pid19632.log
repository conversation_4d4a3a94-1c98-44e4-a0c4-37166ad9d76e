#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 2247360 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=19632, tid=5520
#
# JRE version: OpenJDK Runtime Environment OpenLogic-OpenJDK (17.0.15+6) (build 17.0.15+6-adhoc..jdk17u)
# Java VM: OpenJDK 64-Bit Server VM OpenLogic-OpenJDK (17.0.15+6-adhoc..jdk17u, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1

Host: AMD Ryzen 7 5800X 8-Core Processor             , 16 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Sun May 25 01:57:45 2025 Myanmar Standard Time elapsed time: 15.245702 seconds (0d 0h 0m 15s)

---------------  T H R E A D  ---------------

Current thread (0x00000200aae3a830):  JavaThread "C2 CompilerThread4" daemon [_thread_in_native, id=5520, stack(0x0000002533c00000,0x0000002533d00000)]


Current CompileTask:
C2:  15245 13837       4       org.jetbrains.kotlin.cli.jvm.index.JvmDependenciesIndexImpl::search (374 bytes)

Stack: [0x0000002533c00000,0x0000002533d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x681149]
V  [jvm.dll+0x838b5a]
V  [jvm.dll+0x83a61e]
V  [jvm.dll+0x83ac83]
V  [jvm.dll+0x24837f]
V  [jvm.dll+0xaca54]
V  [jvm.dll+0xad09c]
V  [jvm.dll+0x367a37]
V  [jvm.dll+0x331dda]
V  [jvm.dll+0x33127a]
V  [jvm.dll+0x21a8c1]
V  [jvm.dll+0x219d01]
V  [jvm.dll+0x1a59fd]
V  [jvm.dll+0x229a5d]
V  [jvm.dll+0x227bec]
V  [jvm.dll+0x7eda37]
V  [jvm.dll+0x7e7f8c]
V  [jvm.dll+0x680017]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000200ab210cd0, length=97, elements={
0x0000020041cb0040, 0x00000200628a3000, 0x00000200628a3ad0, 0x00000200628b9530,
0x00000200628b9f00, 0x00000200628bc8d0, 0x00000200628bd290, 0x00000200628c6040,
0x00000200628caf10, 0x00000200628d6000, 0x000002006284d0d0, 0x00000200629ce4e0,
0x00000200629d2a50, 0x0000020062b1afc0, 0x00000200a8717810, 0x00000200aa1ae070,
0x00000200a84182d0, 0x00000200aa2f7470, 0x00000200aa146fb0, 0x00000200aa03ac30,
0x00000200aa03bb60, 0x00000200aa03b650, 0x00000200aa03a210, 0x00000200aa03a720,
0x00000200aa03b140, 0x00000200aa754060, 0x00000200aa7554a0, 0x00000200aa752c20,
0x00000200aa7559b0, 0x00000200aa754f90, 0x00000200aa753b50, 0x00000200aa755ec0,
0x00000200aa752710, 0x00000200aa7568e0, 0x00000200aa753130, 0x00000200aa7563d0,
0x00000200aa756df0, 0x00000200aa757300, 0x00000200aa757810, 0x00000200aa757d20,
0x00000200aa754570, 0x00000200aa758230, 0x00000200aa75a090, 0x00000200aa758740,
0x00000200aa759670, 0x00000200aa758c50, 0x00000200b0151440, 0x00000200b0151950,
0x00000200b0151e60, 0x00000200b0150f30, 0x00000200b014f0d0, 0x00000200b0152880,
0x00000200b014f5e0, 0x00000200b0152370, 0x00000200b0152d90, 0x00000200b01537b0,
0x00000200b0153cc0, 0x00000200b01532a0, 0x00000200b01541d0, 0x00000200b0150a20,
0x00000200b01546e0, 0x00000200b0156540, 0x00000200b0155b20, 0x00000200b0154bf0,
0x00000200b0156a50, 0x00000200b0155100, 0x00000200b0155610, 0x00000200a9306310,
0x00000200a93053e0, 0x00000200a93058f0, 0x00000200a9305e00, 0x00000200a9303fa0,
0x00000200a9303070, 0x00000200a9303a90, 0x00000200a9306820, 0x00000200a9306d30,
0x00000200a9303580, 0x00000200a9307750, 0x00000200a9307240, 0x00000200a9307c60,
0x00000200a93044b0, 0x00000200a9308170, 0x00000200a93049c0, 0x00000200a9308680,
0x00000200a9304ed0, 0x00000200a9308b90, 0x00000200a93090a0, 0x00000200a93095b0,
0x00000200a930a9f0, 0x00000200a9309ac0, 0x00000200a9309fd0, 0x00000200b004e450,
0x00000200b004e960, 0x00000200ad5b20c0, 0x00000200aae3b2d0, 0x00000200aae3b820,
0x00000200aae3a830
}

Java Threads: ( => current thread )
  0x0000020041cb0040 JavaThread "main" [_thread_blocked, id=19720, stack(0x000000252c700000,0x000000252c800000)]
  0x00000200628a3000 JavaThread "Reference Handler" daemon [_thread_blocked, id=1764, stack(0x000000252ce00000,0x000000252cf00000)]
  0x00000200628a3ad0 JavaThread "Finalizer" daemon [_thread_blocked, id=19596, stack(0x000000252cf00000,0x000000252d000000)]
  0x00000200628b9530 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=15244, stack(0x000000252d000000,0x000000252d100000)]
  0x00000200628b9f00 JavaThread "Attach Listener" daemon [_thread_blocked, id=8904, stack(0x000000252d100000,0x000000252d200000)]
  0x00000200628bc8d0 JavaThread "Service Thread" daemon [_thread_blocked, id=11304, stack(0x000000252d200000,0x000000252d300000)]
  0x00000200628bd290 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=12840, stack(0x000000252d300000,0x000000252d400000)]
  0x00000200628c6040 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=19712, stack(0x000000252d400000,0x000000252d500000)]
  0x00000200628caf10 JavaThread "C1 CompilerThread0" daemon [_thread_in_native, id=12160, stack(0x000000252d500000,0x000000252d600000)]
  0x00000200628d6000 JavaThread "Sweeper thread" daemon [_thread_blocked, id=16948, stack(0x000000252d600000,0x000000252d700000)]
  0x000002006284d0d0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=21988, stack(0x000000252d700000,0x000000252d800000)]
  0x00000200629ce4e0 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=9956, stack(0x000000252d800000,0x000000252d900000)]
  0x00000200629d2a50 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=16532, stack(0x000000252d900000,0x000000252da00000)]
  0x0000020062b1afc0 JavaThread "Notification Thread" daemon [_thread_blocked, id=12104, stack(0x000000252dc00000,0x000000252dd00000)]
  0x00000200a8717810 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=19352, stack(0x000000252e500000,0x000000252e600000)]
  0x00000200aa1ae070 JavaThread "Daemon health stats" [_thread_blocked, id=1068, stack(0x000000252e600000,0x000000252e700000)]
  0x00000200a84182d0 JavaThread "Incoming local TCP Connector on port 64004" [_thread_in_native, id=14720, stack(0x000000252df00000,0x000000252e000000)]
  0x00000200aa2f7470 JavaThread "Daemon periodic checks" [_thread_blocked, id=1364, stack(0x000000252e700000,0x000000252e800000)]
  0x00000200aa146fb0 JavaThread "Daemon" [_thread_blocked, id=2904, stack(0x000000252e800000,0x000000252e900000)]
  0x00000200aa03ac30 JavaThread "Handler for socket connection from /127.0.0.1:64004 to /127.0.0.1:64007" [_thread_in_native, id=12616, stack(0x000000252e900000,0x000000252ea00000)]
  0x00000200aa03bb60 JavaThread "Cancel handler" [_thread_blocked, id=21120, stack(0x000000252ea00000,0x000000252eb00000)]
  0x00000200aa03b650 JavaThread "Daemon worker" [_thread_in_Java, id=19256, stack(0x000000252eb00000,0x000000252ec00000)]
  0x00000200aa03a210 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:64004 to /127.0.0.1:64007" [_thread_blocked, id=20340, stack(0x000000252ec00000,0x000000252ed00000)]
  0x00000200aa03a720 JavaThread "Stdin handler" [_thread_blocked, id=7404, stack(0x000000252ed00000,0x000000252ee00000)]
  0x00000200aa03b140 JavaThread "Daemon client event forwarder" [_thread_blocked, id=19236, stack(0x000000252ee00000,0x000000252ef00000)]
  0x00000200aa754060 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=11928, stack(0x000000252ef00000,0x000000252f000000)]
  0x00000200aa7554a0 JavaThread "File lock request listener" [_thread_in_native, id=12116, stack(0x000000252f000000,0x000000252f100000)]
  0x00000200aa752c20 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileHashes)" [_thread_blocked, id=20016, stack(0x000000252db00000,0x000000252dc00000)]
  0x00000200aa7559b0 JavaThread "File lock release action executor" [_thread_blocked, id=17540, stack(0x000000252de00000,0x000000252df00000)]
  0x00000200aa754f90 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileContent)" [_thread_blocked, id=18612, stack(0x000000252f200000,0x000000252f300000)]
  0x00000200aa753b50 JavaThread "Cache worker for file hash cache (D:\Augment_Code\BearLoader4\.gradle\8.11.1\fileHashes)" [_thread_blocked, id=14292, stack(0x000000252f300000,0x000000252f400000)]
  0x00000200aa755ec0 JavaThread "Cache worker for Build Output Cleanup Cache (D:\Augment_Code\BearLoader4\.gradle\buildOutputCleanup)" [_thread_blocked, id=8004, stack(0x000000252f400000,0x000000252f500000)]
  0x00000200aa752710 JavaThread "File watcher server" daemon [_thread_in_native, id=8684, stack(0x000000252fe00000,0x000000252ff00000)]
  0x00000200aa7568e0 JavaThread "File watcher consumer" daemon [_thread_blocked, id=15968, stack(0x000000252ff00000,0x0000002530000000)]
  0x00000200aa753130 JavaThread "jar transforms" [_thread_blocked, id=7212, stack(0x000000252da00000,0x000000252db00000)]
  0x00000200aa7563d0 JavaThread "jar transforms Thread 2" [_thread_blocked, id=10496, stack(0x000000252f100000,0x000000252f200000)]
  0x00000200aa756df0 JavaThread "jar transforms Thread 3" [_thread_blocked, id=17236, stack(0x0000002530000000,0x0000002530100000)]
  0x00000200aa757300 JavaThread "Cache worker for checksums cache (D:\Augment_Code\BearLoader4\.gradle\8.11.1\checksums)" [_thread_blocked, id=21352, stack(0x0000002530300000,0x0000002530400000)]
  0x00000200aa757810 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.11.1\md-rule)" [_thread_blocked, id=2648, stack(0x0000002530400000,0x0000002530500000)]
  0x00000200aa757d20 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.11.1\md-supplier)" [_thread_blocked, id=17308, stack(0x0000002530500000,0x0000002530600000)]
  0x00000200aa754570 JavaThread "jar transforms Thread 4" [_thread_blocked, id=21376, stack(0x0000002530600000,0x0000002530700000)]
  0x00000200aa758230 JavaThread "jar transforms Thread 5" [_thread_blocked, id=18364, stack(0x0000002530700000,0x0000002530800000)]
  0x00000200aa75a090 JavaThread "jar transforms Thread 6" [_thread_blocked, id=16248, stack(0x0000002530800000,0x0000002530900000)]
  0x00000200aa758740 JavaThread "jar transforms Thread 7" [_thread_blocked, id=7064, stack(0x0000002530900000,0x0000002530a00000)]
  0x00000200aa759670 JavaThread "jar transforms Thread 8" [_thread_blocked, id=21844, stack(0x0000002530a00000,0x0000002530b00000)]
  0x00000200aa758c50 JavaThread "jar transforms Thread 9" [_thread_blocked, id=21324, stack(0x0000002530b00000,0x0000002530c00000)]
  0x00000200b0151440 JavaThread "Unconstrained build operations" [_thread_blocked, id=7220, stack(0x0000002530d00000,0x0000002530e00000)]
  0x00000200b0151950 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=16040, stack(0x0000002530e00000,0x0000002530f00000)]
  0x00000200b0151e60 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=8252, stack(0x0000002530f00000,0x0000002531000000)]
  0x00000200b0150f30 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=3384, stack(0x0000002531000000,0x0000002531100000)]
  0x00000200b014f0d0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=16328, stack(0x0000002531100000,0x0000002531200000)]
  0x00000200b0152880 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=10648, stack(0x0000002531200000,0x0000002531300000)]
  0x00000200b014f5e0 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=3572, stack(0x0000002531300000,0x0000002531400000)]
  0x00000200b0152370 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=14112, stack(0x0000002531400000,0x0000002531500000)]
  0x00000200b0152d90 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=17544, stack(0x0000002531500000,0x0000002531600000)]
  0x00000200b01537b0 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=8056, stack(0x0000002531600000,0x0000002531700000)]
  0x00000200b0153cc0 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=13460, stack(0x0000002531700000,0x0000002531800000)]
  0x00000200b01532a0 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=21144, stack(0x0000002531800000,0x0000002531900000)]
  0x00000200b01541d0 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=6724, stack(0x0000002531900000,0x0000002531a00000)]
  0x00000200b0150a20 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=12692, stack(0x0000002531a00000,0x0000002531b00000)]
  0x00000200b01546e0 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=12152, stack(0x0000002531b00000,0x0000002531c00000)]
  0x00000200b0156540 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=17352, stack(0x0000002530c00000,0x0000002530d00000)]
  0x00000200b0155b20 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=12200, stack(0x0000002531c00000,0x0000002531d00000)]
  0x00000200b0154bf0 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=4652, stack(0x0000002531d00000,0x0000002531e00000)]
  0x00000200b0156a50 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=4860, stack(0x0000002531e00000,0x0000002531f00000)]
  0x00000200b0155100 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=10192, stack(0x0000002531f00000,0x0000002532000000)]
  0x00000200b0155610 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=13724, stack(0x0000002532000000,0x0000002532100000)]
  0x00000200a9306310 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=7128, stack(0x0000002532100000,0x0000002532200000)]
  0x00000200a93053e0 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=21048, stack(0x0000002532200000,0x0000002532300000)]
  0x00000200a93058f0 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=16460, stack(0x0000002532300000,0x0000002532400000)]
  0x00000200a9305e00 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=9904, stack(0x0000002532400000,0x0000002532500000)]
  0x00000200a9303fa0 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=21784, stack(0x0000002532500000,0x0000002532600000)]
  0x00000200a9303070 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=18160, stack(0x0000002532600000,0x0000002532700000)]
  0x00000200a9303a90 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=1552, stack(0x0000002532700000,0x0000002532800000)]
  0x00000200a9306820 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=17956, stack(0x0000002532800000,0x0000002532900000)]
  0x00000200a9306d30 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=9968, stack(0x0000002532900000,0x0000002532a00000)]
  0x00000200a9303580 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=21904, stack(0x0000002532a00000,0x0000002532b00000)]
  0x00000200a9307750 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=164, stack(0x0000002532b00000,0x0000002532c00000)]
  0x00000200a9307240 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=17840, stack(0x0000002532c00000,0x0000002532d00000)]
  0x00000200a9307c60 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=8532, stack(0x0000002532d00000,0x0000002532e00000)]
  0x00000200a93044b0 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=6364, stack(0x0000002532e00000,0x0000002532f00000)]
  0x00000200a9308170 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=8796, stack(0x0000002532f00000,0x0000002533000000)]
  0x00000200a93049c0 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=14796, stack(0x0000002533000000,0x0000002533100000)]
  0x00000200a9308680 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=18392, stack(0x0000002533100000,0x0000002533200000)]
  0x00000200a9304ed0 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=22276, stack(0x0000002533200000,0x0000002533300000)]
  0x00000200a9308b90 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=6324, stack(0x0000002533300000,0x0000002533400000)]
  0x00000200a93090a0 JavaThread "Unconstrained build operations Thread 41" [_thread_blocked, id=8764, stack(0x0000002533400000,0x0000002533500000)]
  0x00000200a93095b0 JavaThread "Unconstrained build operations Thread 42" [_thread_blocked, id=18388, stack(0x0000002533500000,0x0000002533600000)]
  0x00000200a930a9f0 JavaThread "Unconstrained build operations Thread 43" [_thread_blocked, id=19676, stack(0x0000002533600000,0x0000002533700000)]
  0x00000200a9309ac0 JavaThread "Unconstrained build operations Thread 44" [_thread_blocked, id=8412, stack(0x0000002533700000,0x0000002533800000)]
  0x00000200a9309fd0 JavaThread "Unconstrained build operations Thread 45" [_thread_blocked, id=12120, stack(0x0000002533800000,0x0000002533900000)]
  0x00000200b004e450 JavaThread "Memory manager" [_thread_blocked, id=17784, stack(0x0000002533900000,0x0000002533a00000)]
  0x00000200b004e960 JavaThread "Kotlin DSL Writer" [_thread_blocked, id=15944, stack(0x0000002533a00000,0x0000002533b00000)]
  0x00000200ad5b20c0 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=20776, stack(0x0000002530100000,0x0000002530200000)]
  0x00000200aae3b2d0 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=21912, stack(0x0000002530200000,0x0000002530300000)]
  0x00000200aae3b820 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=20760, stack(0x0000002533b00000,0x0000002533c00000)]
=>0x00000200aae3a830 JavaThread "C2 CompilerThread4" daemon [_thread_in_native, id=5520, stack(0x0000002533c00000,0x0000002533d00000)]

Other Threads:
  0x000002006289f010 VMThread "VM Thread" [stack: 0x000000252cd00000,0x000000252ce00000] [id=12140] _threads_hazard_ptr=0x00000200ab210cd0
  0x0000020062b1b4a0 WatcherThread [stack: 0x000000252dd00000,0x000000252de00000] [id=14508]
  0x000002004034d8b0 GCTaskThread "GC Thread#0" [stack: 0x000000252c800000,0x000000252c900000] [id=10248]
  0x00000200a877e950 GCTaskThread "GC Thread#1" [stack: 0x000000252e000000,0x000000252e100000] [id=7864]
  0x0000020062b947d0 GCTaskThread "GC Thread#2" [stack: 0x000000252e100000,0x000000252e200000] [id=22508]
  0x00000200a8a6ed20 GCTaskThread "GC Thread#3" [stack: 0x000000252e200000,0x000000252e300000] [id=10320]
  0x00000200a8a6efe0 GCTaskThread "GC Thread#4" [stack: 0x000000252e300000,0x000000252e400000] [id=21116]
  0x00000200a85323c0 GCTaskThread "GC Thread#5" [stack: 0x000000252e400000,0x000000252e500000] [id=1608]
  0x00000200abaaed10 GCTaskThread "GC Thread#6" [stack: 0x000000252f500000,0x000000252f600000] [id=6928]
  0x00000200aa79df30 GCTaskThread "GC Thread#7" [stack: 0x000000252f600000,0x000000252f700000] [id=19864]
  0x00000200aa44a750 GCTaskThread "GC Thread#8" [stack: 0x000000252f700000,0x000000252f800000] [id=4656]
  0x00000200aa68e850 GCTaskThread "GC Thread#9" [stack: 0x000000252f800000,0x000000252f900000] [id=4640]
  0x00000200ac7a8320 GCTaskThread "GC Thread#10" [stack: 0x000000252f900000,0x000000252fa00000] [id=6092]
  0x00000200ac7a9660 GCTaskThread "GC Thread#11" [stack: 0x000000252fa00000,0x000000252fb00000] [id=21424]
  0x00000200ac7a90e0 GCTaskThread "GC Thread#12" [stack: 0x000000252fb00000,0x000000252fc00000] [id=21916]
  0x000002004034e930 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000252c900000,0x000000252ca00000] [id=3336]
  0x0000020041d1e560 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000252ca00000,0x000000252cb00000] [id=15512]
  0x00000200ac7a9920 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000252fc00000,0x000000252fd00000] [id=10620]
  0x00000200ac7a85e0 ConcurrentGCThread "G1 Conc#2" [stack: 0x000000252fd00000,0x000000252fe00000] [id=22148]
  0x0000020062709330 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000252cb00000,0x000000252cc00000] [id=22240]
  0x00000200afc0a390 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000002533d00000,0x0000002533e00000] [id=10868]
  0x00000200afc08c10 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000002533e00000,0x0000002533f00000] [id=7744]
  0x00000200afc091f0 ConcurrentGCThread "G1 Refine#3" [stack: 0x0000002533f00000,0x0000002534000000] [id=19164]
  0x00000200afc094e0 ConcurrentGCThread "G1 Refine#4" [stack: 0x0000002534000000,0x0000002534100000] [id=14036]
  0x00000200afc08f00 ConcurrentGCThread "G1 Refine#5" [stack: 0x0000002534100000,0x0000002534200000] [id=20504]
  0x00000200afc09ac0 ConcurrentGCThread "G1 Refine#6" [stack: 0x0000002534200000,0x0000002534300000] [id=6420]
  0x000002006270ac10 ConcurrentGCThread "G1 Service" [stack: 0x000000252cc00000,0x000000252cd00000] [id=10348]

Threads with active compile tasks:
C2 CompilerThread0    15272 13832       4       org.jetbrains.kotlin.protobuf.GeneratedMessageLite::parseUnknownField (518 bytes)
C1 CompilerThread0    15272 14034       3       org.jetbrains.kotlin.types.TypeSubstitutor::unsafeSubstitute (793 bytes)
C2 CompilerThread4    15272 13837       4       org.jetbrains.kotlin.cli.jvm.index.JvmDependenciesIndexImpl::search (374 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000020063000000-0x0000020063bc0000-0x0000020063bc0000), size 12320768, SharedBaseAddress: 0x0000020063000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000020064000000-0x00000200a4000000, reserved size: 1073741824
Narrow klass base: 0x0000020063000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 16307M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 256M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 245760K, used 186480K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 63 young (64512K), 5 survivors (5120K)
 Metaspace       used 110409K, committed 111232K, reserved 1179648K
  class space    used 14693K, committed 15104K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080100000, 0x0000000080000000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HS|  |TAMS 0x0000000080200000, 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000, 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000, 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080500000, 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000, 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%|HS|  |TAMS 0x0000000080800000, 0x0000000080700000| Complete 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000, 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000, 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000, 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000, 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080ceec00, 0x0000000080d00000| 93%| O|  |TAMS 0x0000000080ceec00, 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000, 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000, 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000, 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000, 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000, 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000, 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000, 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000, 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000, 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000, 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000, 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000, 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000, 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000, 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000, 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000, 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000, 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000, 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000, 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000, 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082200000, 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000, 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000, 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000, 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000, 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000, 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082800000, 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082900000, 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%|HS|  |TAMS 0x0000000082a00000, 0x0000000082900000| Complete 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082b00000, 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000, 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000, 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082e00000, 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082f00000, 0x0000000082e00000| Complete 
|  47|0x0000000082f00000, 0x0000000082f00000, 0x0000000083000000|  0%| F|  |TAMS 0x0000000082f00000, 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083100000, 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083100000, 0x0000000083200000|  0%| F|  |TAMS 0x0000000083100000, 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083200000, 0x0000000083300000|  0%| F|  |TAMS 0x0000000083200000, 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083400000, 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083500000, 0x0000000083400000| Complete 
|  53|0x0000000083500000, 0x0000000083500000, 0x0000000083600000|  0%| F|  |TAMS 0x0000000083500000, 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%| O|  |TAMS 0x0000000083700000, 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%|HS|  |TAMS 0x0000000083800000, 0x0000000083700000| Complete 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%|HS|  |TAMS 0x0000000083900000, 0x0000000083800000| Complete 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%|HS|  |TAMS 0x0000000083a00000, 0x0000000083900000| Complete 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%|HS|  |TAMS 0x0000000083b00000, 0x0000000083a00000| Complete 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%|HS|  |TAMS 0x0000000083c00000, 0x0000000083b00000| Complete 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%|HS|  |TAMS 0x0000000083d00000, 0x0000000083c00000| Complete 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083e00000, 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083f00000, 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000, 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000, 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%|HS|  |TAMS 0x0000000084200000, 0x0000000084100000| Complete 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%|HS|  |TAMS 0x0000000084300000, 0x0000000084200000| Complete 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%|HC|  |TAMS 0x0000000084400000, 0x0000000084300000| Complete 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%|HC|  |TAMS 0x0000000084500000, 0x0000000084400000| Complete 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%|HC|  |TAMS 0x0000000084600000, 0x0000000084500000| Complete 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%|HC|  |TAMS 0x0000000084700000, 0x0000000084600000| Complete 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%|HC|  |TAMS 0x0000000084800000, 0x0000000084700000| Complete 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084900000, 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084a00000, 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000, 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000, 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000, 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000, 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000, 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000, 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000, 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000, 0x0000000085100000| Complete 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085300000, 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000, 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000, 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000, 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000, 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000, 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000, 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000, 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000, 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000, 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085d00000, 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085e00000, 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000, 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000086000000, 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086100000, 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086200000, 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086300000, 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086400000, 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086500000, 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086600000, 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086700000, 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086800000, 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000, 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086a00000, 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086b00000, 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086c00000, 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086d00000, 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086e00000, 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086f00000, 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000087000000, 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087100000, 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087200000, 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087300000, 0x0000000087200000| Complete 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087400000, 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087500000, 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087600000, 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087700000, 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087800000, 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087900000, 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087a00000, 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087b00000, 0x0000000087a00000| Complete 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087c00000, 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087c81400, 0x0000000087d00000| 50%| O|  |TAMS 0x0000000087c81400, 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087d00000, 0x0000000087e00000|  0%| F|  |TAMS 0x0000000087d00000, 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087e00000, 0x0000000087f00000|  0%| F|  |TAMS 0x0000000087e00000, 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000087f00000, 0x0000000088000000|  0%| F|  |TAMS 0x0000000087f00000, 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088000000, 0x0000000088100000|  0%| F|  |TAMS 0x0000000088000000, 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088100000, 0x0000000088200000|  0%| F|  |TAMS 0x0000000088100000, 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088200000, 0x0000000088300000|  0%| F|  |TAMS 0x0000000088200000, 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088300000, 0x0000000088400000|  0%| F|  |TAMS 0x0000000088300000, 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088400000, 0x0000000088500000|  0%| F|  |TAMS 0x0000000088400000, 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088500000, 0x0000000088600000|  0%| F|  |TAMS 0x0000000088500000, 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088600000, 0x0000000088700000|  0%| F|  |TAMS 0x0000000088600000, 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088700000, 0x0000000088800000|  0%| F|  |TAMS 0x0000000088700000, 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088800000, 0x0000000088900000|  0%| F|  |TAMS 0x0000000088800000, 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088900000, 0x0000000088a00000|  0%| F|  |TAMS 0x0000000088900000, 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088a00000, 0x0000000088b00000|  0%| F|  |TAMS 0x0000000088a00000, 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088b00000, 0x0000000088c00000|  0%| F|  |TAMS 0x0000000088b00000, 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088c00000, 0x0000000088d00000|  0%| F|  |TAMS 0x0000000088c00000, 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088d00000, 0x0000000088e00000|  0%| F|  |TAMS 0x0000000088d00000, 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088e00000, 0x0000000088f00000|  0%| F|  |TAMS 0x0000000088e00000, 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000088f00000, 0x0000000089000000|  0%| F|  |TAMS 0x0000000088f00000, 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089000000, 0x0000000089100000|  0%| F|  |TAMS 0x0000000089000000, 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089100000, 0x0000000089200000|  0%| F|  |TAMS 0x0000000089100000, 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089200000, 0x0000000089300000|  0%| F|  |TAMS 0x0000000089200000, 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089300000, 0x0000000089400000|  0%| F|  |TAMS 0x0000000089300000, 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089400000, 0x0000000089500000|  0%| F|  |TAMS 0x0000000089400000, 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089500000, 0x0000000089600000|  0%| F|  |TAMS 0x0000000089500000, 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089600000, 0x0000000089700000|  0%| F|  |TAMS 0x0000000089600000, 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089700000, 0x0000000089800000|  0%| F|  |TAMS 0x0000000089700000, 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089800000, 0x0000000089900000|  0%| F|  |TAMS 0x0000000089800000, 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089900000, 0x0000000089a00000|  0%| F|  |TAMS 0x0000000089900000, 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089a00000, 0x0000000089b00000|  0%| F|  |TAMS 0x0000000089a00000, 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089b00000, 0x0000000089c00000|  0%| F|  |TAMS 0x0000000089b00000, 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089c00000, 0x0000000089d00000|  0%| F|  |TAMS 0x0000000089c00000, 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089d00000, 0x0000000089e00000|  0%| F|  |TAMS 0x0000000089d00000, 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089e00000, 0x0000000089f00000|  0%| F|  |TAMS 0x0000000089e00000, 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x0000000089f00000, 0x000000008a000000|  0%| F|  |TAMS 0x0000000089f00000, 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a000000, 0x000000008a100000|  0%| F|  |TAMS 0x000000008a000000, 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000, 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a200000, 0x000000008a300000|  0%| F|  |TAMS 0x000000008a200000, 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000, 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a400000, 0x000000008a500000|  0%| F|  |TAMS 0x000000008a400000, 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a5ac348, 0x000000008a600000| 67%| S|CS|TAMS 0x000000008a500000, 0x000000008a500000| Complete 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| S|CS|TAMS 0x000000008a600000, 0x000000008a600000| Complete 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| S|CS|TAMS 0x000000008a700000, 0x000000008a700000| Complete 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| S|CS|TAMS 0x000000008a800000, 0x000000008a800000| Complete 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| S|CS|TAMS 0x000000008a900000, 0x000000008a900000| Complete 
| 170|0x000000008aa00000, 0x000000008aa00000, 0x000000008ab00000|  0%| F|  |TAMS 0x000000008aa00000, 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ab00000, 0x000000008ac00000|  0%| F|  |TAMS 0x000000008ab00000, 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ac00000, 0x000000008ad00000|  0%| F|  |TAMS 0x000000008ac00000, 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ad00000, 0x000000008ae00000|  0%| F|  |TAMS 0x000000008ad00000, 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000, 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000, 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b000000, 0x000000008b100000|  0%| F|  |TAMS 0x000000008b000000, 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b100000, 0x000000008b200000|  0%| F|  |TAMS 0x000000008b100000, 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000, 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000, 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000, 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000, 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b680000, 0x000000008b700000| 50%| E|  |TAMS 0x000000008b600000, 0x000000008b600000| Complete 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%| E|CS|TAMS 0x000000008b700000, 0x000000008b700000| Complete 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%| E|CS|TAMS 0x000000008b800000, 0x000000008b800000| Complete 
| 185|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%| E|CS|TAMS 0x000000008b900000, 0x000000008b900000| Complete 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%| E|CS|TAMS 0x000000008ba00000, 0x000000008ba00000| Complete 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| E|CS|TAMS 0x000000008bb00000, 0x000000008bb00000| Complete 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| E|CS|TAMS 0x000000008bc00000, 0x000000008bc00000| Complete 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| E|CS|TAMS 0x000000008bd00000, 0x000000008bd00000| Complete 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| E|CS|TAMS 0x000000008be00000, 0x000000008be00000| Complete 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| E|CS|TAMS 0x000000008bf00000, 0x000000008bf00000| Complete 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| E|CS|TAMS 0x000000008c000000, 0x000000008c000000| Complete 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| E|CS|TAMS 0x000000008c100000, 0x000000008c100000| Complete 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| E|CS|TAMS 0x000000008c200000, 0x000000008c200000| Complete 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| E|CS|TAMS 0x000000008c300000, 0x000000008c300000| Complete 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| E|CS|TAMS 0x000000008c400000, 0x000000008c400000| Complete 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| E|CS|TAMS 0x000000008c500000, 0x000000008c500000| Complete 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| E|CS|TAMS 0x000000008c600000, 0x000000008c600000| Complete 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| E|CS|TAMS 0x000000008c700000, 0x000000008c700000| Complete 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| E|CS|TAMS 0x000000008c800000, 0x000000008c800000| Complete 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| E|CS|TAMS 0x000000008c900000, 0x000000008c900000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| E|CS|TAMS 0x000000008ca00000, 0x000000008ca00000| Complete 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| E|CS|TAMS 0x000000008cb00000, 0x000000008cb00000| Complete 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| E|CS|TAMS 0x000000008cc00000, 0x000000008cc00000| Complete 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| E|CS|TAMS 0x000000008cd00000, 0x000000008cd00000| Complete 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| E|CS|TAMS 0x000000008ce00000, 0x000000008ce00000| Complete 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| E|CS|TAMS 0x000000008cf00000, 0x000000008cf00000| Complete 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| E|CS|TAMS 0x000000008d000000, 0x000000008d000000| Complete 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| E|CS|TAMS 0x000000008d100000, 0x000000008d100000| Complete 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| E|CS|TAMS 0x000000008d200000, 0x000000008d200000| Complete 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| E|CS|TAMS 0x000000008d300000, 0x000000008d300000| Complete 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| E|CS|TAMS 0x000000008d400000, 0x000000008d400000| Complete 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| E|CS|TAMS 0x000000008d500000, 0x000000008d500000| Complete 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| E|CS|TAMS 0x000000008d600000, 0x000000008d600000| Complete 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| E|CS|TAMS 0x000000008d700000, 0x000000008d700000| Complete 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| E|CS|TAMS 0x000000008d800000, 0x000000008d800000| Complete 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| E|CS|TAMS 0x000000008d900000, 0x000000008d900000| Complete 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| E|CS|TAMS 0x000000008da00000, 0x000000008da00000| Complete 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| E|CS|TAMS 0x000000008db00000, 0x000000008db00000| Complete 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| E|CS|TAMS 0x000000008dc00000, 0x000000008dc00000| Complete 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| E|CS|TAMS 0x000000008dd00000, 0x000000008dd00000| Complete 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| E|CS|TAMS 0x000000008de00000, 0x000000008de00000| Complete 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| E|CS|TAMS 0x000000008df00000, 0x000000008df00000| Complete 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| E|CS|TAMS 0x000000008e000000, 0x000000008e000000| Complete 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| E|CS|TAMS 0x000000008e100000, 0x000000008e100000| Complete 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| E|CS|TAMS 0x000000008e200000, 0x000000008e200000| Complete 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| E|CS|TAMS 0x000000008e300000, 0x000000008e300000| Complete 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| E|CS|TAMS 0x000000008e400000, 0x000000008e400000| Complete 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| E|CS|TAMS 0x000000008e500000, 0x000000008e500000| Complete 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| E|CS|TAMS 0x000000008e600000, 0x000000008e600000| Complete 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| E|CS|TAMS 0x000000008e700000, 0x000000008e700000| Complete 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| E|CS|TAMS 0x000000008e800000, 0x000000008e800000| Complete 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| E|CS|TAMS 0x000000008e900000, 0x000000008e900000| Complete 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| E|CS|TAMS 0x000000008ea00000, 0x000000008ea00000| Complete 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| E|CS|TAMS 0x000000008eb00000, 0x000000008eb00000| Complete 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| E|CS|TAMS 0x000000008ec00000, 0x000000008ec00000| Complete 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| E|CS|TAMS 0x000000008ed00000, 0x000000008ed00000| Complete 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| E|CS|TAMS 0x000000008ee00000, 0x000000008ee00000| Complete 
| 255|0x000000008ff00000, 0x0000000090000000, 0x0000000090000000|100%| E|CS|TAMS 0x000000008ff00000, 0x000000008ff00000| Complete 

Card table byte_map: [0x000002005afa0000,0x000002005b3a0000] _byte_map_base: 0x000002005aba0000

Marking Bits (Prev, Next): (CMBitMap*) 0x000002004034ded0, (CMBitMap*) 0x000002004034df10
 Prev Bits: [0x000002005b7a0000, 0x000002005d7a0000)
 Next Bits: [0x000002005d7a0000, 0x000002005f7a0000)

Polling page: 0x0000020041d60000

Metaspace:

Usage:
  Non-class:     93.47 MB used.
      Class:     14.35 MB used.
       Both:    107.82 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      93.88 MB ( 73%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      14.75 MB (  1%) committed,  1 nodes.
             Both:        1.12 GB reserved,     108.62 MB (  9%) committed. 

Chunk freelists:
   Non-Class:  2.12 MB
       Class:  1.23 MB
        Both:  3.36 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 177.50 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 1052.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1736.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 12.
num_chunks_taken_from_freelist: 5316.
num_chunk_merges: 9.
num_chunk_splits: 3837.
num_chunks_enlarged: 2924.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=6733Kb max_used=6733Kb free=112435Kb
 bounds [0x0000020052780000, 0x0000020052e20000, 0x0000020059be0000]
CodeHeap 'profiled nmethods': size=119104Kb used=23741Kb max_used=23741Kb free=95362Kb
 bounds [0x000002004abe0000, 0x000002004c320000, 0x0000020052030000]
CodeHeap 'non-nmethods': size=7488Kb used=4151Kb max_used=4208Kb free=3336Kb
 bounds [0x0000020052030000, 0x0000020052460000, 0x0000020052780000]
 total_blobs=12904 nmethods=11890 adapters=923
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 14.187 Thread 0x00000200a8717810 13936       3       org.jetbrains.kotlin.com.intellij.psi.impl.source.tree.TreeElement::getTreeNext (5 bytes)
Event: 14.187 Thread 0x00000200628caf10 13937       1       org.jetbrains.kotlin.com.intellij.psi.impl.source.tree.TreeElement::getTreePrev (5 bytes)
Event: 14.187 Thread 0x00000200628caf10 nmethod 13937 0x0000020052e07c90 code [0x0000020052e07e20, 0x0000020052e07ef8]
Event: 14.188 Thread 0x00000200a8717810 nmethod 13936 0x000002004c2b0b90 code [0x000002004c2b0d20, 0x000002004c2b0e38]
Event: 14.189 Thread 0x00000200629d2a50 13938       3       org.jetbrains.kotlin.load.java.structure.impl.JavaElementImpl::<init> (18 bytes)
Event: 14.189 Thread 0x00000200629d2a50 nmethod 13938 0x000002004c2b0f10 code [0x000002004c2b10c0, 0x000002004c2b12e8]
Event: 14.189 Thread 0x00000200629ce4e0 13939       3       org.jetbrains.kotlin.it.unimi.dsi.fastutil.objects.ObjectOpenHashSet::rehash (133 bytes)
Event: 14.190 Thread 0x00000200629ce4e0 nmethod 13939 0x000002004c2b1410 code [0x000002004c2b1640, 0x000002004c2b2098]
Event: 14.208 Thread 0x00000200aae3b2d0 nmethod 13848 0x0000020052e07f90 code [0x0000020052e08380, 0x0000020052e0b2c0]
Event: 14.216 Thread 0x00000200a8717810 13940       3       kotlin.enums.EnumEntriesList::getSize (6 bytes)
Event: 14.216 Thread 0x00000200629ce4e0 13941       3       kotlin.collections.AbstractList$IteratorImpl::hasNext (20 bytes)
Event: 14.216 Thread 0x00000200629d2a50 13942       3       kotlin.collections.CollectionsKt___CollectionsKt::toCollection (49 bytes)
Event: 14.216 Thread 0x00000200a8717810 nmethod 13940 0x000002004c2b2490 code [0x000002004c2b2620, 0x000002004c2b2738]
Event: 14.216 Thread 0x00000200629ce4e0 nmethod 13941 0x000002004c2b2810 code [0x000002004c2b29c0, 0x000002004c2b2c48]
Event: 14.216 Thread 0x00000200629d2a50 nmethod 13942 0x000002004c2b2d90 code [0x000002004c2b3060, 0x000002004c2b3e58]
Event: 14.225 Thread 0x00000200628caf10 13943       3       org.jetbrains.kotlin.types.checker.SimpleClassicTypeSystemContext::asFlexibleType (6 bytes)
Event: 14.225 Thread 0x00000200628caf10 nmethod 13943 0x000002004c2b4310 code [0x000002004c2b44a0, 0x000002004c2b45c8]
Event: 14.252 Thread 0x00000200628caf10 13944       3       java.util.Spliterators::emptySpliterator (4 bytes)
Event: 14.253 Thread 0x00000200629d2a50 13945       3       java.lang.StringConcatHelper::prepend (37 bytes)
Event: 14.253 Thread 0x00000200629ce4e0 13946       3       java.lang.String::getBytes (44 bytes)

GC Heap History (20 events):
Event: 8.263 GC heap before
{Heap before GC invocations=24 (full 0):
 garbage-first heap   total 122880K, used 94962K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 42 young (43008K), 4 survivors (4096K)
 Metaspace       used 56656K, committed 57344K, reserved 1114112K
  class space    used 7885K, committed 8256K, reserved 1048576K
}
Event: 8.265 GC heap after
{Heap after GC invocations=25 (full 0):
 garbage-first heap   total 122880K, used 57909K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 56656K, committed 57344K, reserved 1114112K
  class space    used 7885K, committed 8256K, reserved 1048576K
}
Event: 8.391 GC heap before
{Heap before GC invocations=25 (full 0):
 garbage-first heap   total 122880K, used 95797K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 42 young (43008K), 4 survivors (4096K)
 Metaspace       used 56661K, committed 57344K, reserved 1114112K
  class space    used 7885K, committed 8256K, reserved 1048576K
}
Event: 8.394 GC heap after
{Heap after GC invocations=26 (full 0):
 garbage-first heap   total 245760K, used 58779K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 56661K, committed 57344K, reserved 1114112K
  class space    used 7885K, committed 8256K, reserved 1048576K
}
Event: 9.374 GC heap before
{Heap before GC invocations=26 (full 0):
 garbage-first heap   total 245760K, used 203163K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 134 young (137216K), 4 survivors (4096K)
 Metaspace       used 64588K, committed 65280K, reserved 1114112K
  class space    used 8938K, committed 9280K, reserved 1048576K
}
Event: 9.379 GC heap after
{Heap after GC invocations=27 (full 0):
 garbage-first heap   total 245760K, used 77852K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 16 young (16384K), 16 survivors (16384K)
 Metaspace       used 64588K, committed 65280K, reserved 1114112K
  class space    used 8938K, committed 9280K, reserved 1048576K
}
Event: 10.379 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 245760K, used 195612K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 123 young (125952K), 16 survivors (16384K)
 Metaspace       used 75913K, committed 76672K, reserved 1179648K
  class space    used 10739K, committed 11136K, reserved 1048576K
}
Event: 10.385 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total 245760K, used 83226K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 75913K, committed 76672K, reserved 1179648K
  class space    used 10739K, committed 11136K, reserved 1048576K
}
Event: 11.553 GC heap before
{Heap before GC invocations=28 (full 0):
 garbage-first heap   total 245760K, used 191770K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 112 young (114688K), 6 survivors (6144K)
 Metaspace       used 82177K, committed 82944K, reserved 1179648K
  class space    used 11470K, committed 11840K, reserved 1048576K
}
Event: 11.556 GC heap after
{Heap after GC invocations=29 (full 0):
 garbage-first heap   total 245760K, used 87076K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 82177K, committed 82944K, reserved 1179648K
  class space    used 11470K, committed 11840K, reserved 1048576K
}
Event: 12.158 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 245760K, used 212004K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 116 young (118784K), 10 survivors (10240K)
 Metaspace       used 88811K, committed 89664K, reserved 1179648K
  class space    used 12260K, committed 12672K, reserved 1048576K
}
Event: 12.169 GC heap after
{Heap after GC invocations=30 (full 0):
 garbage-first heap   total 245760K, used 108292K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 15 survivors (15360K)
 Metaspace       used 88811K, committed 89664K, reserved 1179648K
  class space    used 12260K, committed 12672K, reserved 1048576K
}
Event: 12.655 GC heap before
{Heap before GC invocations=30 (full 0):
 garbage-first heap   total 245760K, used 178948K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 78 young (79872K), 15 survivors (15360K)
 Metaspace       used 93912K, committed 94720K, reserved 1179648K
  class space    used 12842K, committed 13248K, reserved 1048576K
}
Event: 12.662 GC heap after
{Heap after GC invocations=31 (full 0):
 garbage-first heap   total 245760K, used 124368K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 93912K, committed 94720K, reserved 1179648K
  class space    used 12842K, committed 13248K, reserved 1048576K
}
Event: 13.152 GC heap before
{Heap before GC invocations=32 (full 0):
 garbage-first heap   total 245760K, used 188880K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 73 young (74752K), 8 survivors (8192K)
 Metaspace       used 101690K, committed 102464K, reserved 1179648K
  class space    used 13651K, committed 14016K, reserved 1048576K
}
Event: 13.156 GC heap after
{Heap after GC invocations=33 (full 0):
 garbage-first heap   total 245760K, used 126438K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 101690K, committed 102464K, reserved 1179648K
  class space    used 13651K, committed 14016K, reserved 1048576K
}
Event: 13.208 GC heap before
{Heap before GC invocations=33 (full 0):
 garbage-first heap   total 245760K, used 134630K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 4 survivors (4096K)
 Metaspace       used 102167K, committed 102976K, reserved 1179648K
  class space    used 13704K, committed 14080K, reserved 1048576K
}
Event: 13.210 GC heap after
{Heap after GC invocations=34 (full 0):
 garbage-first heap   total 245760K, used 124981K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 102167K, committed 102976K, reserved 1179648K
  class space    used 13704K, committed 14080K, reserved 1048576K
}
Event: 13.772 GC heap before
{Heap before GC invocations=34 (full 0):
 garbage-first heap   total 245760K, used 195637K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 70 young (71680K), 2 survivors (2048K)
 Metaspace       used 107943K, committed 108736K, reserved 1179648K
  class space    used 14430K, committed 14784K, reserved 1048576K
}
Event: 13.775 GC heap after
{Heap after GC invocations=35 (full 0):
 garbage-first heap   total 245760K, used 128112K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 107943K, committed 108736K, reserved 1179648K
  class space    used 14430K, committed 14784K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.008 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\java.dll
Event: 0.019 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\jsvml.dll
Event: 0.061 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\zip.dll
Event: 0.063 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\instrument.dll
Event: 0.067 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\net.dll
Event: 0.069 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\nio.dll
Event: 0.071 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\zip.dll
Event: 0.209 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\jimage.dll
Event: 0.307 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\verify.dll
Event: 0.452 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 0.456 Loaded shared library C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 1.219 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\management.dll
Event: 1.221 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\management_ext.dll
Event: 1.402 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\extnet.dll
Event: 1.573 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 13.940 Thread 0x00000200aa03b650 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000020052b31178 relative=0x0000000000000c78
Event: 13.940 Thread 0x00000200aa03b650 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000020052b31178 method=org.jetbrains.kotlin.metadata.ProtoBuf$Type.getSerializedSize()I @ 176 c2
Event: 13.940 Thread 0x00000200aa03b650 DEOPT PACKING pc=0x0000020052b31178 sp=0x000000252ebf3130
Event: 13.940 Thread 0x00000200aa03b650 DEOPT UNPACKING pc=0x00000200520869a3 sp=0x000000252ebf30f0 mode 2
Event: 14.098 Thread 0x00000200aa03b650 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000020052c3f444 relative=0x0000000000000124
Event: 14.098 Thread 0x00000200aa03b650 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000020052c3f444 method=org.jetbrains.kotlin.incremental.UtilsKt.record(Lorg/jetbrains/kotlin/incremental/components/LookupTracker;Lorg/jetbrains/kotlin/incremental/compon
Event: 14.098 Thread 0x00000200aa03b650 DEOPT PACKING pc=0x0000020052c3f444 sp=0x000000252ebf4050
Event: 14.098 Thread 0x00000200aa03b650 DEOPT UNPACKING pc=0x00000200520869a3 sp=0x000000252ebf3ff0 mode 2
Event: 14.104 Thread 0x00000200aa03b650 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000020052c3f444 relative=0x0000000000000124
Event: 14.104 Thread 0x00000200aa03b650 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000020052c3f444 method=org.jetbrains.kotlin.incremental.UtilsKt.record(Lorg/jetbrains/kotlin/incremental/components/LookupTracker;Lorg/jetbrains/kotlin/incremental/compon
Event: 14.104 Thread 0x00000200aa03b650 DEOPT PACKING pc=0x0000020052c3f444 sp=0x000000252ebf3f40
Event: 14.104 Thread 0x00000200aa03b650 DEOPT UNPACKING pc=0x00000200520869a3 sp=0x000000252ebf3ee0 mode 2
Event: 14.105 Thread 0x00000200aa03b650 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000020052c3f444 relative=0x0000000000000124
Event: 14.105 Thread 0x00000200aa03b650 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000020052c3f444 method=org.jetbrains.kotlin.incremental.UtilsKt.record(Lorg/jetbrains/kotlin/incremental/components/LookupTracker;Lorg/jetbrains/kotlin/incremental/compon
Event: 14.105 Thread 0x00000200aa03b650 DEOPT PACKING pc=0x0000020052c3f444 sp=0x000000252ebf3f40
Event: 14.105 Thread 0x00000200aa03b650 DEOPT UNPACKING pc=0x00000200520869a3 sp=0x000000252ebf3ee0 mode 2
Event: 14.105 Thread 0x00000200aa03b650 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000020052c3f444 relative=0x0000000000000124
Event: 14.105 Thread 0x00000200aa03b650 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000020052c3f444 method=org.jetbrains.kotlin.incremental.UtilsKt.record(Lorg/jetbrains/kotlin/incremental/components/LookupTracker;Lorg/jetbrains/kotlin/incremental/compon
Event: 14.105 Thread 0x00000200aa03b650 DEOPT PACKING pc=0x0000020052c3f444 sp=0x000000252ebf3f70
Event: 14.105 Thread 0x00000200aa03b650 DEOPT UNPACKING pc=0x00000200520869a3 sp=0x000000252ebf3f10 mode 2

Classes loaded (20 events):
Event: 11.932 Loading class sun/nio/ch/Util$4
Event: 11.933 Loading class sun/nio/ch/Util$4 done
Event: 11.933 Loading class java/io/UTFDataFormatException
Event: 11.933 Loading class java/io/UTFDataFormatException done
Event: 12.218 Loading class java/nio/channels/NonWritableChannelException
Event: 12.218 Loading class java/nio/channels/NonWritableChannelException done
Event: 13.102 Loading class java/text/StringCharacterIterator
Event: 13.104 Loading class java/text/CharacterIterator
Event: 13.104 Loading class java/text/CharacterIterator done
Event: 13.104 Loading class java/text/StringCharacterIterator done
Event: 13.122 Loading class java/io/NotSerializableException
Event: 13.122 Loading class java/io/NotSerializableException done
Event: 13.391 Loading class java/util/EnumMap$EntrySet
Event: 13.393 Loading class java/util/EnumMap$EntrySet done
Event: 13.393 Loading class java/util/EnumMap$EntryIterator
Event: 13.393 Loading class java/util/EnumMap$EnumMapIterator
Event: 13.393 Loading class java/util/EnumMap$EnumMapIterator done
Event: 13.393 Loading class java/util/EnumMap$EntryIterator done
Event: 13.393 Loading class java/util/EnumMap$EntryIterator$Entry
Event: 13.393 Loading class java/util/EnumMap$EntryIterator$Entry done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 11.482 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x0000000088e99cf8}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, long, long)'> (0x0000000088e99cf8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 11.482 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x0000000088ea9710}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x0000000088ea9710) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 11.483 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x0000000088ec0390}: 'long java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x0000000088ec0390) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 11.484 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x0000000088ed4650}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object, long, long)'> (0x0000000088ed4650) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 11.484 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x0000000088edca30}: 'long java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000088edca30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 11.484 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x0000000088ee1bd8}: 'int java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000088ee1bd8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 11.765 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d585108}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000008d585108) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 11.911 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c40efe0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x000000008c40efe0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 11.932 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c26c038}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008c26c038) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 12.117 Thread 0x00000200aa03b650 Implicit null exception at 0x0000020052d32665 to 0x0000020052d34488
Event: 13.335 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x000000008df53048}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000008df53048) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.415 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d73a978}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000008d73a978) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.415 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d745de8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000008d745de8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.415 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d7505e8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008d7505e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.488 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x000000008be45c78}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008be45c78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 14.120 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x000000008bfff868}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008bfff868) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 14.121 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x000000008be0c6e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008be0c6e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 14.121 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x000000008be12a90}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008be12a90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 14.169 Thread 0x00000200aa03b650 Exception <a 'java/lang/NoSuchMethodError'{0x000000008bcd0a38}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object)'> (0x000000008bcd0a38) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 14.228 Thread 0x00000200aa03b650 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000008b892c58}: Found class java.lang.Object, but interface was expected> (0x000000008b892c58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 826]

VM Operations (20 events):
Event: 13.347 Executing VM operation: HandshakeAllThreads
Event: 13.348 Executing VM operation: HandshakeAllThreads done
Event: 13.366 Executing VM operation: HandshakeAllThreads
Event: 13.366 Executing VM operation: HandshakeAllThreads done
Event: 13.369 Executing VM operation: HandshakeAllThreads
Event: 13.369 Executing VM operation: HandshakeAllThreads done
Event: 13.533 Executing VM operation: ICBufferFull
Event: 13.533 Executing VM operation: ICBufferFull done
Event: 13.772 Executing VM operation: G1CollectForAllocation
Event: 13.775 Executing VM operation: G1CollectForAllocation done
Event: 13.820 Executing VM operation: G1PauseRemark
Event: 13.825 Executing VM operation: G1PauseRemark done
Event: 13.845 Executing VM operation: G1PauseCleanup
Event: 13.845 Executing VM operation: G1PauseCleanup done
Event: 14.099 Executing VM operation: ICBufferFull
Event: 14.099 Executing VM operation: ICBufferFull done
Event: 14.184 Executing VM operation: HandshakeAllThreads
Event: 14.184 Executing VM operation: HandshakeAllThreads done
Event: 15.193 Executing VM operation: Cleanup
Event: 15.194 Executing VM operation: Cleanup done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c014910
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c014e10
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c015910
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c016b90
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c018990
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c02bd10
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c041110
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c09f010
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c0e6790
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c125510
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c125d10
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c126290
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c126a90
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c127890
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c127f10
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c137d10
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c139990
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c15a510
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c161010
Event: 13.378 Thread 0x00000200628d6000 flushing  nmethod 0x000002004c163810

Events (20 events):
Event: 7.987 Thread 0x00000200aa03b650 Thread added: 0x00000200a93095b0
Event: 7.989 Thread 0x00000200aa03b650 Thread added: 0x00000200a930a9f0
Event: 7.990 Thread 0x00000200aa03b650 Thread added: 0x00000200a9309ac0
Event: 7.992 Thread 0x00000200aa03b650 Thread added: 0x00000200a9309fd0
Event: 9.010 Thread 0x00000200afc39580 Thread exited: 0x00000200afc39580
Event: 9.129 Thread 0x00000200a8717810 Thread added: 0x00000200afc38590
Event: 9.187 Thread 0x00000200aa03b650 Thread added: 0x00000200b004e450
Event: 10.071 Thread 0x00000200aa03b650 Thread added: 0x00000200b004e960
Event: 10.230 Thread 0x00000200afc38590 Thread exited: 0x00000200afc38590
Event: 10.230 Thread 0x00000200afc36b00 Thread exited: 0x00000200afc36b00
Event: 10.420 Thread 0x00000200629d2a50 Thread added: 0x00000200ad288070
Event: 10.427 Thread 0x00000200628caf10 Thread added: 0x00000200aae3a830
Event: 10.549 Thread 0x00000200629ce4e0 Thread added: 0x00000200ac0abb10
Event: 11.437 Thread 0x00000200ac0abb10 Thread exited: 0x00000200ac0abb10
Event: 11.448 Thread 0x00000200aae3a830 Thread exited: 0x00000200aae3a830
Event: 11.765 Thread 0x00000200ad288070 Thread exited: 0x00000200ad288070
Event: 11.799 Thread 0x00000200a8717810 Thread added: 0x00000200ad5b20c0
Event: 11.971 Thread 0x00000200ad5b20c0 Thread added: 0x00000200aae3b2d0
Event: 11.971 Thread 0x00000200ad5b20c0 Thread added: 0x00000200aae3b820
Event: 11.971 Thread 0x00000200ad5b20c0 Thread added: 0x00000200aae3a830


Dynamic libraries:
0x00007ff758cd0000 - 0x00007ff758cde000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\java.exe
0x00007ffbb5de0000 - 0x00007ffbb6046000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffbb4660000 - 0x00007ffbb4729000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffbb30f0000 - 0x00007ffbb34bc000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffbb34c0000 - 0x00007ffbb360b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffba5970000 - 0x00007ffba5987000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\jli.dll
0x00007ffba0380000 - 0x00007ffba039b000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\VCRUNTIME140.dll
0x00007ffbb4b40000 - 0x00007ffbb4d0a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffbb38a0000 - 0x00007ffbb38c7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffba42a0000 - 0x00007ffba453a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffbb56b0000 - 0x00007ffbb56db000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffbb4820000 - 0x00007ffbb48c9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffbb3760000 - 0x00007ffbb3892000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffbb3610000 - 0x00007ffbb36b3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffbb3f50000 - 0x00007ffbb3f80000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffba3bc0000 - 0x00007ffba3bcc000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\vcruntime140_1.dll
0x00007ffba02f0000 - 0x00007ffba037d000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\msvcp140.dll
0x00007ffb13b80000 - 0x00007ffb147ec000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\server\jvm.dll
0x00007ffbb3f90000 - 0x00007ffbb4042000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffbb4050000 - 0x00007ffbb40f6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffbb5b70000 - 0x00007ffbb5c86000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffbb3bb0000 - 0x00007ffbb3c24000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffbb1ba0000 - 0x00007ffbb1bfe000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffbab6f0000 - 0x00007ffbab726000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffbab730000 - 0x00007ffbab73b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffbb1b80000 - 0x00007ffbb1b94000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffbb1e50000 - 0x00007ffbb1e6a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffba2f10000 - 0x00007ffba2f1a000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\jimage.dll
0x00007ffba4920000 - 0x00007ffba4b61000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffbb4140000 - 0x00007ffbb44c4000 	C:\WINDOWS\System32\combase.dll
0x00007ffbb4730000 - 0x00007ffbb4810000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffba2360000 - 0x00007ffba2399000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffbb36c0000 - 0x00007ffbb3759000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffba0600000 - 0x00007ffba060e000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\instrument.dll
0x00007ffba02c0000 - 0x00007ffba02e5000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\java.dll
0x00007ffb8f440000 - 0x00007ffb8f516000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\jsvml.dll
0x00007ffbb4e80000 - 0x00007ffbb55ad000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffbb2f70000 - 0x00007ffbb30e4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffbb0c10000 - 0x00007ffbb1466000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffbb5c90000 - 0x00007ffbb5d7f000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffbb4d10000 - 0x00007ffbb4d79000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffbb2e80000 - 0x00007ffbb2eaf000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffba02a0000 - 0x00007ffba02b8000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\zip.dll
0x00007ffb9ff20000 - 0x00007ffb9ff39000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\net.dll
0x00007ffbaca00000 - 0x00007ffbacb1e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffbb23c0000 - 0x00007ffbb242a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffb9b670000 - 0x00007ffb9b686000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\nio.dll
0x00007ffb9ff50000 - 0x00007ffb9ff60000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\verify.dll
0x00007ffb9ff60000 - 0x00007ffb9ff87000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffb768a0000 - 0x00007ffb769e4000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffb9b660000 - 0x00007ffb9b669000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\management.dll
0x00007ffb9b650000 - 0x00007ffb9b65b000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\management_ext.dll
0x00007ffbb5d90000 - 0x00007ffbb5d98000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffbb2660000 - 0x00007ffbb267c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffbb1db0000 - 0x00007ffbb1dea000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffbb2460000 - 0x00007ffbb248b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffbb2e50000 - 0x00007ffbb2e76000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffbb2680000 - 0x00007ffbb268c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffbb17d0000 - 0x00007ffbb1803000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffbb4130000 - 0x00007ffbb413a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffbac410000 - 0x00007ffbac42f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffbac330000 - 0x00007ffbac355000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffbb1810000 - 0x00007ffbb1937000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffb9b640000 - 0x00007ffb9b648000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\extnet.dll
0x00007ffb96a60000 - 0x00007ffb96a6e000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\sunmscapi.dll
0x00007ffbb3990000 - 0x00007ffbb3b07000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffbb2870000 - 0x00007ffbb28a0000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffbb2820000 - 0x00007ffbb285f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffb41880000 - 0x00007ffb41888000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\gradle-daemon-main-8.11.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 268435456                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6
PATH=D:\AndroidBuildEnv\SDK\ndk\27.1.12297006\toolchains\llvm\prebuilt\windows-x86_64\bin;D:\AndroidBuildEnv\SDK\ndk\27.1.12297006\build\cmake\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\Programs\Git\cmd\;C:\Program Files\PowerShell\7-preview;C:\Program Files\PowerShell\7-preview\preview;D:\AndroidBuildEnv\SDK\Nodejs\;D:\ProgramData\ComposerSetup\bin\composer.bat;D:\AndroidBuildEnv\dotnet\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Programs\Cursor\cursor\resources\app\bin\;D:\Programs\Microsoft VS Code\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;D:\AndroidBuildEnv\php\;D:\AndroidBuildEnv\SDK\flutter\bin\;D:\AndroidBuildEnv\SDK\Docker\Docker\resources\bin\;D:\AndroidBuildEnv\SDK\cmake\3.22.1\bin;D:\AndroidBuildEnv\SDK\emulator\;D:\AndroidBuildEnv\SDK\cmdline-tools\latest\bin;D:\AndroidBuildEnv\SDK\platform-tools\;D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin;D:\AndroidBuildEnv\SDK\build-tools\36.0.0\;d:\Programs\Trae\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;D:\AndroidBuildEnv\SDK\gradle\gradle-8.11.1\bin\;D:\AndroidBuildEnv\SDK\platforms\;d:\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;D:\DevTools\Windsurf\bin;D:\Cursor_Project\cursor\resources\app\bin
USERNAME=kothar
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 33 Stepping 0, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 0 days 13:03 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 25 model 33 stepping 0 microcode 0xa201016, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for all 16 processors :
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801

Memory: 4k page, system-wide physical 16307M (1432M free)
TotalPageFile size 31477M (AvailPageFile size 52M)
current process WorkingSet (physical memory assigned to process): 543M, peak: 561M
current process commit charge ("private bytes"): 599M, peak: 613M

vm_info: OpenJDK 64-Bit Server VM (17.0.15+6-adhoc..jdk17u) for windows-amd64 JRE (17.0.15+6-adhoc..jdk17u), built on Apr 17 2025 09:38:37 by "" with MS VC++ 16.10 / 16.11 (VS2019)

END.
