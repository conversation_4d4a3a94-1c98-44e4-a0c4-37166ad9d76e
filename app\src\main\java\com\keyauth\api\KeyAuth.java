package com.keyauth.api;

/**
 * Mock implementation of KeyAuth for development purposes
 * This is a placeholder class to make the project buildable
 */
public class KeyAuth {
    
    /**
     * Constructor
     */
    public KeyAuth() {
        // Mock implementation
    }
    
    /**
     * Initialize KeyAuth
     * @return true if initialization was successful, false otherwise
     */
    public boolean initialize() {
        return true;
    }
    
    /**
     * Validate license key
     * @param licenseKey License key to validate
     * @return true if license is valid, false otherwise
     */
    public boolean validateLicense(String licenseKey) {
        // Mock implementation - always return true for development
        return true;
    }
}
