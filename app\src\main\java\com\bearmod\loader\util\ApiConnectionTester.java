package com.bearmod.loader.util;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.bearmod.loader.auth.HWID;

import org.json.JSONObject;

import java.io.IOException;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;

/**
 * Utility class to test API connections
 */
public class ApiConnectionTester {

    private static final String TAG = "ApiConnectionTester";
    private static final String TEST_LICENSE_KEY = "TEST-1234-5678-9012";
    
    // API URLs to test
    private static final String[] API_URLS = {
        "http://38.60.216.136/api/licenses/validate",
        "https://38.60.216.136/api/licenses/validate",
        "http://38.60.216.136/",
        "https://38.60.216.136/"
    };
    
    private final OkHttpClient client;
    private final Executor executor = Executors.newSingleThreadExecutor();
    private final Handler handler = new Handler(Looper.getMainLooper());
    
    /**
     * Interface for connection test results
     */
    public interface ConnectionTestCallback {
        void onResult(String url, boolean success, String message);
        void onComplete(boolean anySuccessful);
    }
    
    /**
     * Constructor
     */
    public ApiConnectionTester() {
        // Create OkHttpClient with logging
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        
        client = new OkHttpClient.Builder()
                .addInterceptor(loggingInterceptor)
                .build();
    }
    
    /**
     * Test all API connections
     * @param callback Callback for test results
     */
    public void testAllConnections(ConnectionTestCallback callback) {
        executor.execute(() -> {
            boolean anySuccessful = false;
            
            for (String url : API_URLS) {
                boolean success = testConnection(url, callback);
                if (success) {
                    anySuccessful = true;
                }
            }
            
            final boolean finalAnySuccessful = anySuccessful;
            handler.post(() -> callback.onComplete(finalAnySuccessful));
        });
    }
    
    /**
     * Test a specific API connection
     * @param url URL to test
     * @param callback Callback for test result
     * @return true if connection was successful, false otherwise
     */
    private boolean testConnection(String url, ConnectionTestCallback callback) {
        try {
            // First try a simple GET request
            Request getRequest = new Request.Builder()
                    .url(url)
                    .get()
                    .build();
            
            try (Response response = client.newCall(getRequest).execute()) {
                if (response.isSuccessful()) {
                    String message = "GET request successful: " + response.code();
                    Log.d(TAG, url + " - " + message);
                    
                    final String finalMessage = message;
                    handler.post(() -> callback.onResult(url, true, finalMessage));
                    return true;
                }
            } catch (IOException e) {
                Log.d(TAG, url + " - GET request failed: " + e.getMessage());
            }
            
            // If GET fails, try a POST request with license validation
            FormBody.Builder formBuilder = new FormBody.Builder()
                    .add("action", "validate")
                    .add("license_key", TEST_LICENSE_KEY)
                    .add("device_id", HWID.getHWID())
                    .add("app_name", "com.bearmod.loader")
                    .add("app_version", "1.0");
            
            Request postRequest = new Request.Builder()
                    .url(url)
                    .post(formBuilder.build())
                    .build();
            
            try (Response response = client.newCall(postRequest).execute()) {
                String message;
                boolean success = false;
                
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    try {
                        JSONObject json = new JSONObject(responseBody);
                        boolean apiSuccess = json.optBoolean("success", false);
                        
                        if (apiSuccess) {
                            message = "POST request successful with API success";
                            success = true;
                        } else {
                            String errorMsg = json.optString("message", "Unknown error");
                            message = "POST request successful but API returned error: " + errorMsg;
                        }
                    } catch (Exception e) {
                        message = "POST request successful but response is not valid JSON: " + e.getMessage();
                    }
                } else {
                    message = "POST request failed with code: " + response.code();
                }
                
                Log.d(TAG, url + " - " + message);
                final String finalMessage = message;
                final boolean finalSuccess = success;
                
                handler.post(() -> callback.onResult(url, finalSuccess, finalMessage));
                return success;
            }
        } catch (Exception e) {
            String message = "Connection test failed: " + e.getMessage();
            Log.e(TAG, url + " - " + message);
            
            final String finalMessage = message;
            handler.post(() -> callback.onResult(url, false, finalMessage));
            return false;
        }
    }
}
