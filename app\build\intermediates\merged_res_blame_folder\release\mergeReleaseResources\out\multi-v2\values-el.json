{"logs": [{"outputFile": "com.bearmod.loader.app-mergeReleaseResources-55:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\84e22dff9d3244eb5f30d696cec58c96\\transformed\\appcompat-1.7.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,438,549,666,751,857,980,1069,1154,1245,1338,1433,1527,1627,1720,1815,1912,2003,2094,2179,2290,2399,2501,2612,2722,2830,3001,10053", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "433,544,661,746,852,975,1064,1149,1240,1333,1428,1522,1622,1715,1810,1907,1998,2089,2174,2285,2394,2496,2607,2717,2825,2996,3096,10134"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95f2be59c03f86888596a581fb73e161\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3543,3641,3744,3844,3947,4055,4161,10383", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3636,3739,3839,3942,4050,4156,4273,10479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a95a1075bc0403a1ca2712bac24841aa\\transformed\\material-1.12.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,358,444,529,625,712,814,931,1017,1080,1146,1246,1328,1391,1482,1545,1610,1672,1741,1803,1857,1995,2052,2113,2167,2240,2393,2478,2557,2653,2737,2821,2960,3041,3126,3267,3357,3443,3498,3549,3615,3693,3778,3849,3932,4004,4084,4164,4235,4342,4434,4506,4603,4700,4774,4848,4950,5006,5093,5165,5253,5345,5407,5471,5534,5604,5720,5829,5938,6043,6102,6157,6248,6336,6411", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,85,84,95,86,101,116,85,62,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,78,95,83,83,138,80,84,140,89,85,54,50,65,77,84,70,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90,87,74,80", "endOffsets": "265,353,439,524,620,707,809,926,1012,1075,1141,1241,1323,1386,1477,1540,1605,1667,1736,1798,1852,1990,2047,2108,2162,2235,2388,2473,2552,2648,2732,2816,2955,3036,3121,3262,3352,3438,3493,3544,3610,3688,3773,3844,3927,3999,4079,4159,4230,4337,4429,4501,4598,4695,4769,4843,4945,5001,5088,5160,5248,5340,5402,5466,5529,5599,5715,5824,5933,6038,6097,6152,6243,6331,6406,6487"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3101,3189,3275,3360,3456,4278,4380,4497,4583,4646,4712,4812,4894,4957,5048,5111,5176,5238,5307,5369,5423,5561,5618,5679,5733,5806,5959,6044,6123,6219,6303,6387,6526,6607,6692,6833,6923,7009,7064,7115,7181,7259,7344,7415,7498,7570,7650,7730,7801,7908,8000,8072,8169,8266,8340,8414,8516,8572,8659,8731,8819,8911,8973,9037,9100,9170,9286,9395,9504,9609,9668,9962,10139,10227,10302", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "endColumns": "12,87,85,84,95,86,101,116,85,62,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,78,95,83,83,138,80,84,140,89,85,54,50,65,77,84,70,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90,87,74,80", "endOffsets": "315,3184,3270,3355,3451,3538,4375,4492,4578,4641,4707,4807,4889,4952,5043,5106,5171,5233,5302,5364,5418,5556,5613,5674,5728,5801,5954,6039,6118,6214,6298,6382,6521,6602,6687,6828,6918,7004,7059,7110,7176,7254,7339,7410,7493,7565,7645,7725,7796,7903,7995,8067,8164,8261,8335,8409,8511,8567,8654,8726,8814,8906,8968,9032,9095,9165,9281,9390,9499,9604,9663,9718,10048,10222,10297,10378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b19140129864dc831a67762e70345bb\\transformed\\navigation-ui-2.9.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,172", "endColumns": "116,121", "endOffsets": "167,289"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9723,9840", "endColumns": "116,121", "endOffsets": "9835,9957"}}]}]}