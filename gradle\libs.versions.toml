[versions]
agp = "8.10.0"
concurrentFutures = "1.2.0"
converterGson = "3.0.0"
guava = "30.1-android"
json = "20250107"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.0"
keyauthJava = "1.1"
material = "1.12.0"
constraintlayout = "2.2.1"
navigation = "2.9.0"
lifecycle = "2.9.0"
retrofit = "3.0.0"
okhttp = "4.12.0"
gson = "2.13.1"
retrofitVersion = "3.0.0"
room = "2.7.1"
glide = "4.16.0"
roomCompiler = "2.7.1"
roomKtx = "2.7.1"
roomRuntime = "2.7.1"
shimmer = "0.5.0"
lottie = "6.6.6"
keyauthJavaApi = "1.1"
frida = "16.1.4"
docker = "1.0.0"
swiperefreshlayoutVersion = "1.1.0"
websocket = "1.6.0"
work = "2.10.1"
workGuava = "2.10.1"
swiperefreshlayout = "1.1.0"
fragment = "1.8.7"
recyclerview = "1.4.0"
mockito = "5.8.0"

[libraries]
#noinspection SimilarGradleDependency
concurrent-futures = { module = "androidx.concurrent:concurrent-futures", version.ref = "concurrentFutures" }
#noinspection SimilarGradleDependency
converter-gson-v2110 = { module = "com.squareup.retrofit2:converter-gson", version.ref = "converterGson" }
guava = { module = "com.google.guava:guava", version.ref = "guava" }
json = { module = "org.json:json", version.ref = "json" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
keyauth-java = { module = "com.github.Tentoxa:KeyAuth-Java", version.ref = "keyauthJava" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
keyauth-java-api = { module = "com.github.Tentoxa:KeyAuth-Java", version.ref = "keyauthJavaApi" }


# Navigation components
navigation-fragment = { group = "androidx.navigation", name = "navigation-fragment", version.ref = "navigation" }
navigation-ui = { group = "androidx.navigation", name = "navigation-ui", version.ref = "navigation" }

# Lifecycle components
lifecycle-viewmodel = { group = "androidx.lifecycle", name = "lifecycle-viewmodel", version.ref = "lifecycle" }
lifecycle-livedata = { group = "androidx.lifecycle", name = "lifecycle-livedata", version.ref = "lifecycle" }

# Network
#noinspection SimilarGradleDependency
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
#noinspection SimilarGradleDependency
retrofit-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofit" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
okhttp-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "gson" }

# Database
#noinspection SimilarGradleDependency
retrofit-v2110 = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofitVersion" }
#noinspection SimilarGradleDependency
room-compiler-v271 = { module = "androidx.room:room-compiler", version.ref = "roomCompiler" }
#noinspection SimilarGradleDependency
room-ktx-v271 = { module = "androidx.room:room-ktx", version.ref = "roomKtx" }
#noinspection SimilarGradleDependency
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
#noinspection SimilarGradleDependency
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
#noinspection SimilarGradleDependency
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }

# Image loading
glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glide" }

# UI effects
#noinspection SimilarGradleDependency
room-runtime-v271 = { module = "androidx.room:room-runtime", version.ref = "roomRuntime" }
shimmer = { group = "com.facebook.shimmer", name = "shimmer", version.ref = "shimmer" }
lottie = { group = "com.airbnb.android", name = "lottie", version.ref = "lottie" }

# WebSocket
swiperefreshlayout = { module = "androidx.swiperefreshlayout:swiperefreshlayout", version.ref = "swiperefreshlayoutVersion" }
websocket = { group = "org.java-websocket", name = "Java-WebSocket", version.ref = "websocket" }

# WorkManager
work-guava = { group = "androidx.work", name = "work-guava", version.ref = "workGuava" }
work-runtime = { group = "androidx.work", name = "work-runtime", version.ref = "work" }
fragment = { group = "androidx.fragment", name = "fragment", version.ref = "fragment" }
recyclerview = { group = "androidx.recyclerview", name = "recyclerview", version.ref = "recyclerview" }
work-runtime-ktx = { module = "androidx.work:work-runtime-ktx", version.ref = "work" }

# Testing
mockito-core = { group = "org.mockito", name = "mockito-core", version.ref = "mockito" }
mockito-android = { group = "org.mockito", name = "mockito-android", version.ref = "mockito" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
