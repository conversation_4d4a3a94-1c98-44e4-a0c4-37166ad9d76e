<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_download_modern" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_download_modern.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_download_modern_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="334" endOffset="53"/></Target><Target id="@+id/app_bar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="116" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="17" startOffset="8" endLine="26" endOffset="54"/></Target><Target id="@+id/search_layout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="29" startOffset="8" endLine="55" endOffset="63"/></Target><Target id="@+id/et_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="47" startOffset="12" endLine="53" endOffset="63"/></Target><Target id="@+id/chip_group_filters" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="64" startOffset="12" endLine="112" endOffset="56"/></Target><Target id="@+id/chip_filter_all" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="73" startOffset="16" endLine="81" endOffset="48"/></Target><Target id="@+id/chip_filter_latest" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="83" startOffset="16" endLine="90" endOffset="48"/></Target><Target id="@+id/chip_sort_date" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="92" startOffset="16" endLine="100" endOffset="54"/></Target><Target id="@+id/chip_sort_size" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="102" startOffset="16" endLine="110" endOffset="54"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="119" startOffset="4" endLine="196" endOffset="59"/></Target><Target id="@+id/progress_loading" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="135" startOffset="16" endLine="143" endOffset="68"/></Target><Target id="@+id/layout_empty_state" view="LinearLayout"><Expressions/><location startLine="146" startOffset="16" endLine="180" endOffset="30"/></Target><Target id="@+id/rv_releases" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="183" startOffset="16" endLine="190" endOffset="59"/></Target><Target id="@+id/card_download_info" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="199" startOffset="4" endLine="332" endOffset="55"/></Target><Target id="@+id/tv_apk_size" view="TextView"><Expressions/><location startLine="246" startOffset="20" endLine="254" endOffset="56"/></Target><Target id="@+id/tv_obb_size" view="TextView"><Expressions/><location startLine="271" startOffset="20" endLine="279" endOffset="57"/></Target><Target id="@+id/tv_total_size" view="TextView"><Expressions/><location startLine="307" startOffset="20" endLine="316" endOffset="59"/></Target><Target id="@+id/btn_download" view="Button"><Expressions/><location startLine="322" startOffset="12" endLine="328" endOffset="57"/></Target></Targets></Layout>