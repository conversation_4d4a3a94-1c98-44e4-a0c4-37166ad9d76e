<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="drawer_test_activity">Drawer Test Activity</string>
    <string name="this_activity_tests_the_navigation_drawer_functionality">This activity tests the navigation drawer functionality.</string>
    <string name="open_drawer">Open Drawer</string>
    <string name="keyauth_api_connection_test">KeyAuth API Connection Test</string>
    <string name="this_test_will_verify_if_the_keyauth_api_domain_is_working_correctly_it_will_attempt_to_connect_to_the_api_and_validate_a_test_license_key">This test will verify if the KeyAuth API domain is working correctly. It will attempt to connect to the API and validate a test license key.</string>
    <string name="api_domains">API Domains:</string>
    <string name="primary">Primary:</string>
    <string name="https_keyauth_win_api_1_2">https://keyauth.win/api/1.2/</string>
    <string name="alternate">Alternate:</string>
    <string name="custom_domain">Custom Domain:</string>
    <string name="license_server_connection">License Server Connection</string>
    <string name="test_connection_to_the_license_server">Test connection to the license server</string>
    <string name="license_validation">License Validation</string>
    <string name="validate_your_license_key">Validate your license key</string>
    <string name="validate_license">Validate License</string>
    <string name="server_information">Server Information</string>
    <string name="current_server_configuration">Current server configuration</string>
</resources>