package com.bearmod.loader.ui.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bearmod.loader.R;

import java.util.List;

/**
 * Adapter for API test results
 */
public class ApiTestResultAdapter extends RecyclerView.Adapter<ApiTestResultAdapter.ViewHolder> {

    /**
     * API test result class
     */
    public static class ApiTestResult {
        private final String url;
        private final boolean success;
        private final String message;

        public ApiTestResult(String url, boolean success, String message) {
            this.url = url;
            this.success = success;
            this.message = message;
        }

        public String getUrl() {
            return url;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }
    }

    private final List<ApiTestResult> results;

    /**
     * Constructor
     * @param results List of API test results
     */
    public ApiTestResultAdapter(List<ApiTestResult> results) {
        this.results = results;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_api_test_result, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ApiTestResult result = results.get(position);
        holder.tvUrl.setText(result.getUrl());
        holder.tvMessage.setText(result.getMessage());
        
        if (result.isSuccess()) {
            holder.ivStatus.setImageResource(R.drawable.ic_check_circle);
            holder.ivStatus.setColorFilter(holder.itemView.getContext().getResources().getColor(R.color.success));
        } else {
            holder.ivStatus.setImageResource(R.drawable.ic_error);
            holder.ivStatus.setColorFilter(holder.itemView.getContext().getResources().getColor(R.color.error));
        }
    }

    @Override
    public int getItemCount() {
        return results.size();
    }

    /**
     * ViewHolder for API test results
     */
    public static class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvUrl;
        private final TextView tvMessage;
        private final ImageView ivStatus;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvUrl = itemView.findViewById(R.id.tv_url);
            tvMessage = itemView.findViewById(R.id.tv_message);
            ivStatus = itemView.findViewById(R.id.iv_status);
        }
    }
}
