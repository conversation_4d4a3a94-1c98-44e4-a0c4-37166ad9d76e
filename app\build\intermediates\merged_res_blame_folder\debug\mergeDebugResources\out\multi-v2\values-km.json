{"logs": [{"outputFile": "com.bearmod.loader.app-mergeDebugResources-55:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\84e22dff9d3244eb5f30d696cec58c96\\transformed\\appcompat-1.7.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,421,520,630,717,820,941,1019,1095,1186,1279,1371,1465,1565,1658,1753,1847,1938,2029,2112,2216,2320,2420,2529,2638,2747,2909,9731", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "416,515,625,712,815,936,1014,1090,1181,1274,1366,1460,1560,1653,1748,1842,1933,2024,2107,2211,2315,2415,2524,2633,2742,2904,3002,9810"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a95a1075bc0403a1ca2712bac24841aa\\transformed\\material-1.12.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,661,761,873,953,1019,1084,1178,1248,1310,1397,1460,1525,1584,1649,1710,1767,1886,1944,2005,2062,2133,2263,2349,2425,2510,2592,2670,2808,2883,2954,3104,3201,3279,3334,3390,3456,3536,3626,3697,3782,3861,3938,4008,4083,4195,4283,4356,4456,4555,4629,4705,4812,4866,4956,5029,5120,5216,5278,5342,5405,5476,5575,5673,5765,5861,5919,5979,6062,6144,6222", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,79,78,78,99,111,79,65,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,75,84,81,77,137,74,70,149,96,77,54,55,65,79,89,70,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82,81,77,75", "endOffsets": "264,342,418,498,577,656,756,868,948,1014,1079,1173,1243,1305,1392,1455,1520,1579,1644,1705,1762,1881,1939,2000,2057,2128,2258,2344,2420,2505,2587,2665,2803,2878,2949,3099,3196,3274,3329,3385,3451,3531,3621,3692,3777,3856,3933,4003,4078,4190,4278,4351,4451,4550,4624,4700,4807,4861,4951,5024,5115,5211,5273,5337,5400,5471,5570,5668,5760,5856,5914,5974,6057,6139,6217,6293"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3085,3161,3241,3320,4120,4220,4332,4412,4478,4543,4637,4707,4769,4856,4919,4984,5043,5108,5169,5226,5345,5403,5464,5521,5592,5722,5808,5884,5969,6051,6129,6267,6342,6413,6563,6660,6738,6793,6849,6915,6995,7085,7156,7241,7320,7397,7467,7542,7654,7742,7815,7915,8014,8088,8164,8271,8325,8415,8488,8579,8675,8737,8801,8864,8935,9034,9132,9224,9320,9378,9648,9815,9897,9975", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "endColumns": "12,77,75,79,78,78,99,111,79,65,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,75,84,81,77,137,74,70,149,96,77,54,55,65,79,89,70,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82,81,77,75", "endOffsets": "314,3080,3156,3236,3315,3394,4215,4327,4407,4473,4538,4632,4702,4764,4851,4914,4979,5038,5103,5164,5221,5340,5398,5459,5516,5587,5717,5803,5879,5964,6046,6124,6262,6337,6408,6558,6655,6733,6788,6844,6910,6990,7080,7151,7236,7315,7392,7462,7537,7649,7737,7810,7910,8009,8083,8159,8266,8320,8410,8483,8574,8670,8732,8796,8859,8930,9029,9127,9219,9315,9373,9433,9726,9892,9970,10046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95f2be59c03f86888596a581fb73e161\\transformed\\core-1.13.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "38,39,40,41,42,43,44,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3399,3494,3597,3695,3795,3896,4008,10051", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3489,3592,3690,3790,3891,4003,4115,10147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b19140129864dc831a67762e70345bb\\transformed\\navigation-ui-2.9.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,104", "endOffsets": "155,260"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9438,9543", "endColumns": "104,104", "endOffsets": "9538,9643"}}]}]}