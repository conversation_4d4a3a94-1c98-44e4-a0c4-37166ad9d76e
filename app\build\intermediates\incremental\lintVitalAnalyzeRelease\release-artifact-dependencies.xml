<dependencies>
  <compile
      roots="androidx.databinding:viewbinding:8.10.0@aar,androidx.navigation:navigation-runtime-android:2.9.0@aar,androidx.navigation:navigation-common-android:2.9.0@aar,androidx.navigation:navigation-fragment:2.9.0@aar,androidx.navigation:navigation-ui:2.9.0@aar,com.google.android.material:material:1.12.0@aar,com.airbnb.android:lottie:6.6.6@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.constraintlayout:constraintlayout:2.2.1@aar,androidx.work:work-runtime-ktx:2.10.1@aar,androidx.work:work-runtime:2.10.1@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar,com.github.bumptech.glide:glide:4.16.0@aar,androidx.viewpager2:viewpager2:1.1.0-beta02@aar,androidx.fragment:fragment-ktx:1.8.7@aar,androidx.fragment:fragment:1.8.7@aar,androidx.activity:activity-ktx:1.8.1@aar,androidx.activity:activity:1.8.1@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.recyclerview:recyclerview:1.4.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.transition:transition:1.5.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar,com.squareup.retrofit2:converter-gson:3.0.0@jar,com.squareup.retrofit2:retrofit:3.0.0@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.google.code.gson:gson:2.13.1@jar,androidx.room:room-runtime-android:2.7.1@aar,androidx.room:room-common-jvm:2.7.1@jar,androidx.room:room-ktx:2.7.1@aar,com.facebook.shimmer:shimmer:0.5.0@aar,org.json:json:20250107@jar,androidx.concurrent:concurrent-futures:1.2.0@jar,com.google.guava:guava:30.1-android@jar,org.java-websocket:Java-WebSocket:1.6.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.interpolator:interpolator:1.0.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.collection:collection-ktx:1.4.2@jar,androidx.collection:collection-jvm:1.4.2@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.sqlite:sqlite-framework-android:2.5.0@aar,androidx.sqlite:sqlite-android:2.5.0@aar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.21@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,org.jspecify:jspecify:1.0.0@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,com.google.errorprone:error_prone_annotations:2.38.0@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,androidx.exifinterface:exifinterface:1.3.6@aar,com.google.guava:failureaccess:1.0.1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-compat-qual:2.5.5@jar,com.google.j2objc:j2objc-annotations:1.3@jar,org.slf4j:slf4j-api:2.0.13@jar">
    <dependency
        name="androidx.databinding:viewbinding:8.10.0@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.navigation:navigation-runtime-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-runtime-android"/>
    <dependency
        name="androidx.navigation:navigation-common-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-common-android"/>
    <dependency
        name="androidx.navigation:navigation-fragment:2.9.0@aar"
        simpleName="androidx.navigation:navigation-fragment"/>
    <dependency
        name="androidx.navigation:navigation-ui:2.9.0@aar"
        simpleName="androidx.navigation:navigation-ui"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="com.airbnb.android:lottie:6.6.6@aar"
        simpleName="com.airbnb.android:lottie"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.2.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.10.1@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.10.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.8.7@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.fragment:fragment:1.8.7@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.4.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:3.0.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:3.0.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.google.code.gson:gson:2.13.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="androidx.room:room-runtime-android:2.7.1@aar"
        simpleName="androidx.room:room-runtime-android"/>
    <dependency
        name="androidx.room:room-common-jvm:2.7.1@jar"
        simpleName="androidx.room:room-common-jvm"/>
    <dependency
        name="androidx.room:room-ktx:2.7.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="com.facebook.shimmer:shimmer:0.5.0@aar"
        simpleName="com.facebook.shimmer:shimmer"/>
    <dependency
        name="org.json:json:20250107@jar"
        simpleName="org.json:json"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.2.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="com.google.guava:guava:30.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="org.java-websocket:Java-WebSocket:1.6.0@jar"
        simpleName="org.java-websocket:Java-WebSocket"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.2@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.sqlite:sqlite-framework-android:2.5.0@aar"
        simpleName="androidx.sqlite:sqlite-framework-android"/>
    <dependency
        name="androidx.sqlite:sqlite-android:2.5.0@aar"
        simpleName="androidx.sqlite:sqlite-android"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.38.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-compat-qual:2.5.5@jar"
        simpleName="org.checkerframework:checker-compat-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:1.3@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
    <dependency
        name="org.slf4j:slf4j-api:2.0.13@jar"
        simpleName="org.slf4j:slf4j-api"/>
  </compile>
  <package
      roots="androidx.databinding:viewbinding:8.10.0@aar,androidx.navigation:navigation-runtime-android:2.9.0@aar,androidx.navigation:navigation-common-android:2.9.0@aar,androidx.navigation:navigation-fragment:2.9.0@aar,androidx.navigation:navigation-ui:2.9.0@aar,com.google.android.material:material:1.12.0@aar,androidx.constraintlayout:constraintlayout:2.2.1@aar,com.airbnb.android:lottie:6.6.6@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.work:work-runtime-ktx:2.10.1@aar,androidx.work:work-runtime:2.10.1@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar,com.github.bumptech.glide:glide:4.16.0@aar,androidx.fragment:fragment-ktx:1.8.7@aar,androidx.recyclerview:recyclerview:1.4.0@aar,androidx.viewpager2:viewpager2:1.1.0-beta02@aar,androidx.fragment:fragment:1.8.7@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.activity:activity-ktx:1.8.1@aar,androidx.activity:activity:1.8.1@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.transition:transition:1.5.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.window:window:1.0.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar,androidx.lifecycle:lifecycle-process:2.9.0@aar,androidx.lifecycle:lifecycle-service:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar,com.squareup.retrofit2:converter-gson:3.0.0@jar,com.squareup.retrofit2:retrofit:3.0.0@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.google.code.gson:gson:2.13.1@jar,androidx.room:room-runtime-android:2.7.1@aar,androidx.room:room-common-jvm:2.7.1@jar,androidx.room:room-ktx:2.7.1@aar,com.facebook.shimmer:shimmer:0.5.0@aar,org.json:json:20250107@jar,androidx.profileinstaller:profileinstaller:1.4.0@aar,androidx.concurrent:concurrent-futures-ktx:1.2.0@jar,androidx.concurrent:concurrent-futures:1.2.0@jar,com.google.guava:guava:30.1-android@jar,org.java-websocket:Java-WebSocket:1.6.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.cardview:cardview:1.0.0@aar,androidx.constraintlayout:constraintlayout-core:1.1.1@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.interpolator:interpolator:1.0.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.exifinterface:exifinterface:1.3.6@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.collection:collection-ktx:1.4.2@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-jvm:1.4.2@jar,androidx.tracing:tracing-ktx:1.2.0@aar,androidx.tracing:tracing:1.2.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.sqlite:sqlite-framework-android:2.5.0@aar,androidx.sqlite:sqlite-android:2.5.0@aar,androidx.annotation:annotation-experimental:1.4.1@aar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar,androidx.core:core-viewtree:1.0.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,androidx.annotation:annotation-jvm:1.9.1@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.21@jar,com.google.errorprone:error_prone_annotations:2.38.0@jar,org.jspecify:jspecify:1.0.0@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-compat-qual:2.5.5@jar,com.google.j2objc:j2objc-annotations:1.3@jar,org.slf4j:slf4j-api:2.0.13@jar,org.jetbrains:annotations:23.0.0@jar">
    <dependency
        name="androidx.databinding:viewbinding:8.10.0@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.navigation:navigation-runtime-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-runtime-android"/>
    <dependency
        name="androidx.navigation:navigation-common-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-common-android"/>
    <dependency
        name="androidx.navigation:navigation-fragment:2.9.0@aar"
        simpleName="androidx.navigation:navigation-fragment"/>
    <dependency
        name="androidx.navigation:navigation-ui:2.9.0@aar"
        simpleName="androidx.navigation:navigation-ui"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.2.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.airbnb.android:lottie:6.6.6@aar"
        simpleName="com.airbnb.android:lottie"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.10.1@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.10.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.8.7@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.4.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment:1.8.7@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.window:window:1.0.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:3.0.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:3.0.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.google.code.gson:gson:2.13.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="androidx.room:room-runtime-android:2.7.1@aar"
        simpleName="androidx.room:room-runtime-android"/>
    <dependency
        name="androidx.room:room-common-jvm:2.7.1@jar"
        simpleName="androidx.room:room-common-jvm"/>
    <dependency
        name="androidx.room:room-ktx:2.7.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="com.facebook.shimmer:shimmer:0.5.0@aar"
        simpleName="com.facebook.shimmer:shimmer"/>
    <dependency
        name="org.json:json:20250107@jar"
        simpleName="org.json:json"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.concurrent:concurrent-futures-ktx:1.2.0@jar"
        simpleName="androidx.concurrent:concurrent-futures-ktx"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.2.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="com.google.guava:guava:30.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="org.java-websocket:Java-WebSocket:1.6.0@jar"
        simpleName="org.java-websocket:Java-WebSocket"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.1.1@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.2@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.2.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.sqlite:sqlite-framework-android:2.5.0@aar"
        simpleName="androidx.sqlite:sqlite-framework-android"/>
    <dependency
        name="androidx.sqlite:sqlite-android:2.5.0@aar"
        simpleName="androidx.sqlite:sqlite-android"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.38.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-compat-qual:2.5.5@jar"
        simpleName="org.checkerframework:checker-compat-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:1.3@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
    <dependency
        name="org.slf4j:slf4j-api:2.0.13@jar"
        simpleName="org.slf4j:slf4j-api"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
  </package>
</dependencies>
