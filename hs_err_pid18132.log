#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 255852544 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3618), pid=18132, tid=21368
#
# JRE version: OpenJDK Runtime Environment OpenLogic-OpenJDK (17.0.15+6) (build 17.0.15+6-adhoc..jdk17u)
# Java VM: OpenJDK 64-Bit Server VM OpenLogic-OpenJDK (17.0.15+6-adhoc..jdk17u, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1

Host: AMD Ryzen 7 5800X 8-Core Processor             , 16 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Mon May 12 07:01:45 2025 Myanmar Standard Time elapsed time: 23.992855 seconds (0d 0h 0m 23s)

---------------  T H R E A D  ---------------

Current thread (0x000002577f6ad7e0):  VMThread "VM Thread" [stack: 0x0000006b49d00000,0x0000006b49e00000] [id=21368]

Stack: [0x0000006b49d00000,0x0000006b49e00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x681149]
V  [jvm.dll+0x838b5a]
V  [jvm.dll+0x83a61e]
V  [jvm.dll+0x83ac83]
V  [jvm.dll+0x24837f]
V  [jvm.dll+0x67de79]
V  [jvm.dll+0x67294a]
V  [jvm.dll+0x30836b]
V  [jvm.dll+0x30f856]
V  [jvm.dll+0x35fbce]
V  [jvm.dll+0x35fe0f]
V  [jvm.dll+0x2df31c]
V  [jvm.dll+0x2dd6df]
V  [jvm.dll+0x2dce0c]
V  [jvm.dll+0x3205ab]
V  [jvm.dll+0x83f17d]
V  [jvm.dll+0x83fec2]
V  [jvm.dll+0x8403ef]
V  [jvm.dll+0x8407d4]
V  [jvm.dll+0x8408a0]
V  [jvm.dll+0x7e7f8c]
V  [jvm.dll+0x680017]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]

VM_Operation (0x0000006b4bbf5a50): G1CollectForAllocation, mode: safepoint, requested by thread 0x0000025741157cb0


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002574afb5630, length=85, elements={
0x000002575fa8ff40, 0x000002577f6b3610, 0x000002577f6b4c50, 0x000002577f6c6ef0,
0x000002577f6c97d0, 0x000002577f6cac10, 0x000002577f6ce060, 0x000002577f6d0340,
0x000002577f6dd2e0, 0x000002577f6eb840, 0x00000257411590f0, 0x0000025741080b50,
0x000002574115a020, 0x0000025741159600, 0x00000257411586d0, 0x0000025741157290,
0x0000025741158be0, 0x0000025741157cb0, 0x0000025747c55310, 0x0000025747c52a90,
0x0000025747c55820, 0x0000025747c55d30, 0x0000025747c56750, 0x0000025747c56c60,
0x0000025747c543e0, 0x0000025747c534b0, 0x0000025747c57170, 0x0000025747c57680,
0x0000025747c59f00, 0x0000025747c599f0, 0x000002574b910a10, 0x000002574b911430,
0x000002574b911940, 0x000002574b912870, 0x000002574f4ce2b0, 0x000002574f4d4250,
0x000002574e078f50, 0x000002575918bdf0, 0x000002574e3e7da0, 0x000002574e3e8cd0,
0x000002574e3e87c0, 0x000002574e3e82b0, 0x000002574e3e9c00, 0x000002574e3e91e0,
0x000002574e3ea110, 0x000002574e3e7380, 0x000002574e3e6e70, 0x000002574e3ebf70,
0x000002574df86380, 0x000002574df8a340, 0x000002574df8b330, 0x000002574df8a890,
0x000002574df8ade0, 0x000002574e3ecea0, 0x000002574e3ec480, 0x000002574e3ed3b0,
0x000002574e3ed8c0, 0x000002574e3ea620, 0x000002574e3eddd0, 0x000002574e3ee2e0,
0x000002574e3ee7f0, 0x000002574e3eb040, 0x000002574e3eb550, 0x000002574e075290,
0x000002574e0757a0, 0x000002574e074870, 0x000002574e075cb0, 0x000002574e0770f0,
0x000002574e074360, 0x000002574e0761c0, 0x000002574e073e50, 0x000002574e0766d0,
0x000002574e076be0, 0x000002574e073940, 0x000002574e077b10, 0x000002574c523a80,
0x000002574c527c50, 0x000002574c52a9e0, 0x000002574c528670, 0x000002574c528160,
0x000002574c528b80, 0x000002574c5295a0, 0x000002574c529090, 0x000002574c529ab0,
0x000002574c529fc0
}

Java Threads: ( => current thread )
  0x000002575fa8ff40 JavaThread "main" [_thread_blocked, id=5296, stack(0x0000006b49700000,0x0000006b49800000)]
  0x000002577f6b3610 JavaThread "Reference Handler" daemon [_thread_blocked, id=22592, stack(0x0000006b49e00000,0x0000006b49f00000)]
  0x000002577f6b4c50 JavaThread "Finalizer" daemon [_thread_blocked, id=20172, stack(0x0000006b49f00000,0x0000006b4a000000)]
  0x000002577f6c6ef0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=14356, stack(0x0000006b4a000000,0x0000006b4a100000)]
  0x000002577f6c97d0 JavaThread "Attach Listener" daemon [_thread_blocked, id=19416, stack(0x0000006b4a100000,0x0000006b4a200000)]
  0x000002577f6cac10 JavaThread "Service Thread" daemon [_thread_blocked, id=18252, stack(0x0000006b4a200000,0x0000006b4a300000)]
  0x000002577f6ce060 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=16744, stack(0x0000006b4a300000,0x0000006b4a400000)]
  0x000002577f6d0340 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=7216, stack(0x0000006b4a400000,0x0000006b4a500000)]
  0x000002577f6dd2e0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=21452, stack(0x0000006b4a500000,0x0000006b4a600000)]
  0x000002577f6eb840 JavaThread "Sweeper thread" daemon [_thread_blocked, id=23404, stack(0x0000006b4a600000,0x0000006b4a700000)]
  0x00000257411590f0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=20968, stack(0x0000006b4a700000,0x0000006b4a800000)]
  0x0000025741080b50 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=22980, stack(0x0000006b4a800000,0x0000006b4a900000)]
  0x000002574115a020 JavaThread "Notification Thread" daemon [_thread_blocked, id=20956, stack(0x0000006b4ac00000,0x0000006b4ad00000)]
  0x0000025741159600 JavaThread "Daemon health stats" [_thread_blocked, id=18452, stack(0x0000006b4b600000,0x0000006b4b700000)]
  0x00000257411586d0 JavaThread "Incoming local TCP Connector on port 52215" [_thread_in_native, id=19244, stack(0x0000006b4b700000,0x0000006b4b800000)]
  0x0000025741157290 JavaThread "Daemon periodic checks" [_thread_blocked, id=23440, stack(0x0000006b4b800000,0x0000006b4b900000)]
  0x0000025741158be0 JavaThread "Daemon" [_thread_blocked, id=21612, stack(0x0000006b4b900000,0x0000006b4ba00000)]
  0x0000025741157cb0 JavaThread "Daemon worker" [_thread_blocked, id=21664, stack(0x0000006b4bb00000,0x0000006b4bc00000)]
  0x0000025747c55310 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=22056, stack(0x0000006b4bf00000,0x0000006b4c000000)]
  0x0000025747c52a90 JavaThread "File lock request listener" [_thread_in_native, id=21004, stack(0x0000006b4c000000,0x0000006b4c100000)]
  0x0000025747c55820 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileHashes)" [_thread_blocked, id=11852, stack(0x0000006b4aa00000,0x0000006b4ab00000)]
  0x0000025747c55d30 JavaThread "File lock release action executor" [_thread_blocked, id=22084, stack(0x0000006b4ae00000,0x0000006b4af00000)]
  0x0000025747c56750 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileContent)" [_thread_blocked, id=22092, stack(0x0000006b4c100000,0x0000006b4c200000)]
  0x0000025747c56c60 JavaThread "File watcher server" daemon [_thread_in_native, id=22748, stack(0x0000006b4d000000,0x0000006b4d100000)]
  0x0000025747c543e0 JavaThread "File watcher consumer" daemon [_thread_blocked, id=18388, stack(0x0000006b4d100000,0x0000006b4d200000)]
  0x0000025747c534b0 JavaThread "jar transforms" [_thread_blocked, id=12592, stack(0x0000006b4d200000,0x0000006b4d300000)]
  0x0000025747c57170 JavaThread "jar transforms Thread 2" [_thread_blocked, id=21744, stack(0x0000006b4d300000,0x0000006b4d400000)]
  0x0000025747c57680 JavaThread "jar transforms Thread 3" [_thread_blocked, id=20764, stack(0x0000006b4d400000,0x0000006b4d500000)]
  0x0000025747c59f00 JavaThread "jar transforms Thread 4" [_thread_blocked, id=11736, stack(0x0000006b4db00000,0x0000006b4dc00000)]
  0x0000025747c599f0 JavaThread "jar transforms Thread 5" [_thread_blocked, id=21816, stack(0x0000006b4da00000,0x0000006b4db00000)]
  0x000002574b910a10 JavaThread "jar transforms Thread 6" [_thread_blocked, id=23044, stack(0x0000006b4dd00000,0x0000006b4de00000)]
  0x000002574b911430 JavaThread "jar transforms Thread 7" [_thread_blocked, id=23152, stack(0x0000006b4de00000,0x0000006b4df00000)]
  0x000002574b911940 JavaThread "jar transforms Thread 8" [_thread_blocked, id=4472, stack(0x0000006b4df00000,0x0000006b4e000000)]
  0x000002574b912870 JavaThread "jar transforms Thread 9" [_thread_blocked, id=9164, stack(0x0000006b4e000000,0x0000006b4e100000)]
  0x000002574f4ce2b0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=21924, stack(0x0000006b4ab00000,0x0000006b4ac00000)]
  0x000002574f4d4250 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=23548, stack(0x0000006b4c200000,0x0000006b4c300000)]
  0x000002574e078f50 JavaThread "Memory manager" [_thread_blocked, id=21048, stack(0x0000006b51100000,0x0000006b51200000)]
  0x000002575918bdf0 JavaThread "VFS cleanup" [_thread_blocked, id=16512, stack(0x0000006b4b500000,0x0000006b4b600000)]
  0x000002574e3e7da0 JavaThread "Handler for socket connection from /127.0.0.1:52215 to /127.0.0.1:52313" [_thread_in_native, id=21444, stack(0x0000006b4a900000,0x0000006b4aa00000)]
  0x000002574e3e8cd0 JavaThread "Cancel handler" [_thread_blocked, id=19072, stack(0x0000006b4af00000,0x0000006b4b000000)]
  0x000002574e3e87c0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:52215 to /127.0.0.1:52313" [_thread_blocked, id=19648, stack(0x0000006b4ba00000,0x0000006b4bb00000)]
  0x000002574e3e82b0 JavaThread "Stdin handler" [_thread_blocked, id=6200, stack(0x0000006b4bc00000,0x0000006b4bd00000)]
  0x000002574e3e9c00 JavaThread "Daemon client event forwarder" [_thread_blocked, id=21772, stack(0x0000006b4bd00000,0x0000006b4be00000)]
  0x000002574e3e91e0 JavaThread "Cache worker for file hash cache (D:\BearMod_Project\BearLoader4\.gradle\8.11.1\fileHashes)" [_thread_blocked, id=9420, stack(0x0000006b4be00000,0x0000006b4bf00000)]
  0x000002574e3ea110 JavaThread "Cache worker for Build Output Cleanup Cache (D:\BearMod_Project\BearLoader4\.gradle\buildOutputCleanup)" [_thread_blocked, id=10944, stack(0x0000006b4c300000,0x0000006b4c400000)]
  0x000002574e3e7380 JavaThread "Cache worker for checksums cache (D:\BearMod_Project\BearLoader4\.gradle\8.11.1\checksums)" [_thread_blocked, id=16724, stack(0x0000006b4d500000,0x0000006b4d600000)]
  0x000002574e3e6e70 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.11.1\md-rule)" [_thread_blocked, id=21016, stack(0x0000006b4c400000,0x0000006b4c500000)]
  0x000002574e3ebf70 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.11.1\md-supplier)" [_thread_blocked, id=20260, stack(0x0000006b4c500000,0x0000006b4c600000)]
  0x000002574df86380 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=21104, stack(0x0000006b4c600000,0x0000006b4c700000)]
  0x000002574df8a340 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=22588, stack(0x0000006b4d600000,0x0000006b4d700000)]
  0x000002574df8b330 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=20152, stack(0x0000006b4d900000,0x0000006b4da00000)]
  0x000002574df8a890 JavaThread "C2 CompilerThread4" daemon [_thread_blocked, id=21376, stack(0x0000006b4dc00000,0x0000006b4dd00000)]
  0x000002574df8ade0 JavaThread "C2 CompilerThread5" daemon [_thread_blocked, id=10496, stack(0x0000006b4e500000,0x0000006b4e600000)]
  0x000002574e3ecea0 JavaThread "jar transforms Thread 10" [_thread_blocked, id=9288, stack(0x0000006b4e600000,0x0000006b4e700000)]
  0x000002574e3ec480 JavaThread "jar transforms Thread 11" [_thread_blocked, id=412, stack(0x0000006b4e700000,0x0000006b4e800000)]
  0x000002574e3ed3b0 JavaThread "Unconstrained build operations" [_thread_blocked, id=21212, stack(0x0000006b4e800000,0x0000006b4e900000)]
  0x000002574e3ed8c0 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=22568, stack(0x0000006b4e900000,0x0000006b4ea00000)]
  0x000002574e3ea620 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=18560, stack(0x0000006b4ea00000,0x0000006b4eb00000)]
  0x000002574e3eddd0 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=3996, stack(0x0000006b4eb00000,0x0000006b4ec00000)]
  0x000002574e3ee2e0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=22644, stack(0x0000006b4ec00000,0x0000006b4ed00000)]
  0x000002574e3ee7f0 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=9516, stack(0x0000006b4ed00000,0x0000006b4ee00000)]
  0x000002574e3eb040 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=15892, stack(0x0000006b4ee00000,0x0000006b4ef00000)]
  0x000002574e3eb550 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=4132, stack(0x0000006b4ef00000,0x0000006b4f000000)]
  0x000002574e075290 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=900, stack(0x0000006b4f000000,0x0000006b4f100000)]
  0x000002574e0757a0 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=23228, stack(0x0000006b4f100000,0x0000006b4f200000)]
  0x000002574e074870 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=22720, stack(0x0000006b4f200000,0x0000006b4f300000)]
  0x000002574e075cb0 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=9804, stack(0x0000006b4f300000,0x0000006b4f400000)]
  0x000002574e0770f0 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=21540, stack(0x0000006b4f400000,0x0000006b4f500000)]
  0x000002574e074360 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=7032, stack(0x0000006b4f500000,0x0000006b4f600000)]
  0x000002574e0761c0 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=3448, stack(0x0000006b4f600000,0x0000006b4f700000)]
  0x000002574e073e50 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=7240, stack(0x0000006b4f700000,0x0000006b4f800000)]
  0x000002574e0766d0 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=21932, stack(0x0000006b4f800000,0x0000006b4f900000)]
  0x000002574e076be0 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=21032, stack(0x0000006b4f900000,0x0000006b4fa00000)]
  0x000002574e073940 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=1628, stack(0x0000006b4fa00000,0x0000006b4fb00000)]
  0x000002574e077b10 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=20904, stack(0x0000006b4fb00000,0x0000006b4fc00000)]
  0x000002574c523a80 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=17040, stack(0x0000006b4fc00000,0x0000006b4fd00000)]
  0x000002574c527c50 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=11668, stack(0x0000006b4fd00000,0x0000006b4fe00000)]
  0x000002574c52a9e0 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=22540, stack(0x0000006b4fe00000,0x0000006b4ff00000)]
  0x000002574c528670 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=17104, stack(0x0000006b4ff00000,0x0000006b50000000)]
  0x000002574c528160 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=11084, stack(0x0000006b50000000,0x0000006b50100000)]
  0x000002574c528b80 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=5724, stack(0x0000006b50100000,0x0000006b50200000)]
  0x000002574c5295a0 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=21976, stack(0x0000006b50200000,0x0000006b50300000)]
  0x000002574c529090 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=21012, stack(0x0000006b50300000,0x0000006b50400000)]
  0x000002574c529ab0 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=23532, stack(0x0000006b50400000,0x0000006b50500000)]
  0x000002574c529fc0 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=21764, stack(0x0000006b50500000,0x0000006b50600000)]

Other Threads:
=>0x000002577f6ad7e0 VMThread "VM Thread" [stack: 0x0000006b49d00000,0x0000006b49e00000] [id=21368]
  0x0000025741241b90 WatcherThread [stack: 0x0000006b4ad00000,0x0000006b4ae00000] [id=11128]
  0x000002575faf0500 GCTaskThread "GC Thread#0" [stack: 0x0000006b49800000,0x0000006b49900000] [id=8232]
  0x0000025741c664e0 GCTaskThread "GC Thread#1" [stack: 0x0000006b4b000000,0x0000006b4b100000] [id=20472]
  0x00000257460a6800 GCTaskThread "GC Thread#2" [stack: 0x0000006b4b100000,0x0000006b4b200000] [id=18364]
  0x00000257460b8860 GCTaskThread "GC Thread#3" [stack: 0x0000006b4b200000,0x0000006b4b300000] [id=22856]
  0x00000257417a1e40 GCTaskThread "GC Thread#4" [stack: 0x0000006b4b300000,0x0000006b4b400000] [id=22836]
  0x0000025741c004d0 GCTaskThread "GC Thread#5" [stack: 0x0000006b4b400000,0x0000006b4b500000] [id=23212]
  0x000002574652bec0 GCTaskThread "GC Thread#6" [stack: 0x0000006b4c700000,0x0000006b4c800000] [id=23056]
  0x000002574652c700 GCTaskThread "GC Thread#7" [stack: 0x0000006b4c800000,0x0000006b4c900000] [id=7140]
  0x000002574652b100 GCTaskThread "GC Thread#8" [stack: 0x0000006b4c900000,0x0000006b4ca00000] [id=10288]
  0x000002574652c180 GCTaskThread "GC Thread#9" [stack: 0x0000006b4ca00000,0x0000006b4cb00000] [id=9472]
  0x000002574652c9c0 GCTaskThread "GC Thread#10" [stack: 0x0000006b4cb00000,0x0000006b4cc00000] [id=17344]
  0x0000025747d82010 GCTaskThread "GC Thread#11" [stack: 0x0000006b4cc00000,0x0000006b4cd00000] [id=22752]
  0x0000025747d83350 GCTaskThread "GC Thread#12" [stack: 0x0000006b4cd00000,0x0000006b4ce00000] [id=22844]
  0x000002575d94ce80 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000006b49900000,0x0000006b49a00000] [id=23220]
  0x000002575d94df50 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000006b49a00000,0x0000006b49b00000] [id=22740]
  0x0000025747d83e50 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000006b4ce00000,0x0000006b4cf00000] [id=12904]
  0x0000025747d80f90 ConcurrentGCThread "G1 Conc#2" [stack: 0x0000006b4cf00000,0x0000006b4d000000] [id=23436]
  0x000002575fb3da20 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000006b49b00000,0x0000006b49c00000] [id=17392]
  0x000002574b355e70 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000006b4e100000,0x0000006b4e200000] [id=21812]
  0x000002574b356160 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000006b4e200000,0x0000006b4e300000] [id=23096]
  0x000002574b356450 ConcurrentGCThread "G1 Refine#3" [stack: 0x0000006b4e300000,0x0000006b4e400000] [id=17860]
  0x000002574b356a30 ConcurrentGCThread "G1 Refine#4" [stack: 0x0000006b4e400000,0x0000006b4e500000] [id=20424]
  0x000002574ee64c20 ConcurrentGCThread "G1 Refine#5" [stack: 0x0000006b4d700000,0x0000006b4d800000] [id=9608]
  0x000002574ee63790 ConcurrentGCThread "G1 Refine#6" [stack: 0x0000006b4d800000,0x0000006b4d900000] [id=6348]
  0x000002575fb3e360 ConcurrentGCThread "G1 Service" [stack: 0x0000006b49c00000,0x0000006b49d00000] [id=23452]

Threads with active compile tasks:
C1 CompilerThread1    24005 24155       3       org.jetbrains.kotlin.com.intellij.util.io.FileAccessorCache$Handle::<init> (43 bytes)
C2 CompilerThread2    24005 24143       4       org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.jar.CoreJarHandler::<init> (236 bytes)
C2 CompilerThread3    24005 24142       4       java.util.zip.ZipEntry::setExtra0 (493 bytes)
C2 CompilerThread4    24005 24144   !   4       sun.nio.fs.WindowsFileAttributes::get (236 bytes)
C2 CompilerThread5    24005 24154       4       sun.nio.fs.WindowsFileAttributes::fromFileAttributeData (104 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000002575fa7cda0] Threads_lock - owner thread: 0x000002577f6ad7e0
[0x000002575fa7ca40] Heap_lock - owner thread: 0x0000025741157cb0

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000025700000000-0x0000025700bc0000-0x0000025700bc0000), size 12320768, SharedBaseAddress: 0x0000025700000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000025701000000-0x0000025741000000, reserved size: 1073741824
Narrow klass base: 0x0000025700000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 16307M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 256M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 262144K, used 209869K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 160604K, committed 161664K, reserved 1245184K
  class space    used 20527K, committed 21056K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080100000, 0x0000000080100000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HS|  |TAMS 0x0000000080200000, 0x0000000080200000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000, 0x0000000080300000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000, 0x0000000080400000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080500000, 0x0000000080500000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000, 0x0000000080600000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080700000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%|HS|  |TAMS 0x0000000080800000, 0x0000000080800000| Complete 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000, 0x0000000080900000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000, 0x0000000080a00000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000, 0x0000000080b00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000, 0x0000000080c00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080cf3600, 0x0000000080d00000| 95%| O|  |TAMS 0x0000000080cf3600, 0x0000000080cf3600| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000, 0x0000000080e00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000, 0x0000000080f00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000, 0x0000000081000000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000, 0x0000000081100000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000, 0x0000000081200000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000, 0x0000000081300000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000, 0x0000000081400000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000, 0x0000000081500000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000, 0x0000000081600000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000, 0x0000000081700000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000, 0x0000000081800000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000, 0x0000000081900000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000, 0x0000000081a00000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000, 0x0000000081b00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000, 0x0000000081c00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000, 0x0000000081d00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000, 0x0000000081e00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000, 0x0000000081f00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000, 0x0000000082000000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000, 0x0000000082100000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082100000, 0x0000000082200000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000, 0x0000000082300000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000, 0x0000000082400000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000, 0x0000000082500000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000, 0x0000000082600000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000, 0x0000000082700000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082800000, 0x0000000082800000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082900000, 0x0000000082900000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082a00000, 0x0000000082a00000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082b00000, 0x0000000082b00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000, 0x0000000082c00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000, 0x0000000082d00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082e00000, 0x0000000082e00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082f00000, 0x0000000082f00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000083000000, 0x0000000083000000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083100000, 0x0000000083100000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083200000, 0x0000000083200000| Untracked 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%| O|  |TAMS 0x0000000083300000, 0x0000000083300000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083400000, 0x0000000083400000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083500000, 0x0000000083500000| Untracked 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%| O|  |TAMS 0x0000000083600000, 0x0000000083600000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%| O|  |TAMS 0x0000000083700000, 0x0000000083700000| Untracked 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083800000, 0x0000000083800000| Untracked 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083900000, 0x0000000083900000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083a00000, 0x0000000083a00000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083a00000, 0x0000000083b00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083c00000, 0x0000000083c00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083d00000, 0x0000000083d00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083e00000, 0x0000000083e00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083f00000, 0x0000000083f00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000, 0x0000000084000000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000, 0x0000000084100000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000, 0x0000000084200000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000, 0x0000000084300000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084400000, 0x0000000084400000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084400000, 0x0000000084500000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084500000, 0x0000000084600000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084600000, 0x0000000084700000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084700000, 0x0000000084800000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084800000, 0x0000000084900000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084900000, 0x0000000084a00000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084a00000, 0x0000000084b00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000, 0x0000000084c00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000, 0x0000000084d00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000, 0x0000000084e00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000, 0x0000000084f00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000, 0x0000000085000000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%|HS|  |TAMS 0x0000000085000000, 0x0000000085100000| Complete 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%|HS|  |TAMS 0x0000000085100000, 0x0000000085200000| Complete 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%|HS|  |TAMS 0x0000000085200000, 0x0000000085300000| Complete 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%|HS|  |TAMS 0x0000000085300000, 0x0000000085400000| Complete 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085400000, 0x0000000085500000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000, 0x0000000085600000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000, 0x0000000085700000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000, 0x0000000085800000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000, 0x0000000085900000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000, 0x0000000085a00000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000, 0x0000000085b00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000, 0x0000000085c00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085d00000, 0x0000000085d00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085e00000, 0x0000000085e00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000, 0x0000000085f00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000086000000, 0x0000000086000000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086100000, 0x0000000086100000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086200000, 0x0000000086200000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086300000, 0x0000000086300000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086400000, 0x0000000086400000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x00000000864f8c00, 0x0000000086500000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086500000, 0x0000000086600000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086600000, 0x0000000086700000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086700000, 0x0000000086800000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086800000, 0x0000000086900000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086900000, 0x0000000086a00000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086a00000, 0x0000000086b00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086b00000, 0x0000000086c00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086c00000, 0x0000000086d00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086d00000, 0x0000000086e00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%|HS|  |TAMS 0x0000000086e00000, 0x0000000086f00000| Complete 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000086f00000, 0x0000000087000000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087000000, 0x0000000087100000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087100000, 0x0000000087200000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087200000, 0x0000000087300000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087300000, 0x0000000087400000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087400000, 0x0000000087500000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087500000, 0x0000000087600000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000, 0x0000000087700000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087700000, 0x0000000087800000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087800000, 0x0000000087900000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087900000, 0x0000000087a00000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087a00000, 0x0000000087b00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087b00000, 0x0000000087c00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087c00000, 0x0000000087d00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087d00000, 0x0000000087e00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087e00000, 0x0000000087f00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000087f00000, 0x0000000088000000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088000000, 0x0000000088100000| Untracked 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088100000, 0x0000000088200000| Untracked 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| O|  |TAMS 0x0000000088200000, 0x0000000088300000| Untracked 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088300000, 0x0000000088400000| Untracked 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| O|  |TAMS 0x0000000088400000, 0x0000000088500000| Untracked 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| O|  |TAMS 0x0000000088500000, 0x0000000088600000| Untracked 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| O|  |TAMS 0x0000000088600000, 0x0000000088700000| Untracked 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| O|  |TAMS 0x0000000088700000, 0x0000000088800000| Untracked 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088800000, 0x0000000088900000| Untracked 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088900000, 0x0000000088a00000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088a00000, 0x0000000088b00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088b00000, 0x0000000088c00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088c00000, 0x0000000088d00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%|HS|  |TAMS 0x0000000088d00000, 0x0000000088e00000| Complete 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|  |TAMS 0x0000000088e00000, 0x0000000088f00000| Untracked 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| O|  |TAMS 0x0000000088f00000, 0x0000000089000000| Untracked 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089000000, 0x0000000089100000| Untracked 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089100000, 0x0000000089200000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089200000, 0x0000000089300000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%|HS|  |TAMS 0x0000000089300000, 0x0000000089400000| Complete 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089400000, 0x0000000089500000| Untracked 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089500000, 0x0000000089600000| Untracked 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089600000, 0x0000000089700000| Untracked 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| O|  |TAMS 0x0000000089700000, 0x0000000089800000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089800000, 0x0000000089900000| Untracked 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089900000, 0x0000000089a00000| Untracked 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| O|  |TAMS 0x0000000089a00000, 0x0000000089b00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089b00000, 0x0000000089c00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089c00000, 0x0000000089d00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089d00000, 0x0000000089e00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089e00000, 0x0000000089f00000| Untracked 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x0000000089f00000, 0x000000008a000000| Untracked 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|  |TAMS 0x000000008a000000, 0x000000008a100000| Untracked 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| O|  |TAMS 0x000000008a100000, 0x000000008a200000| Untracked 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| O|  |TAMS 0x000000008a200000, 0x000000008a300000| Untracked 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%| O|  |TAMS 0x000000008a300000, 0x000000008a400000| Untracked 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| O|  |TAMS 0x000000008a400000, 0x000000008a500000| Untracked 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a500000, 0x000000008a600000| Untracked 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| O|  |TAMS 0x000000008a600000, 0x000000008a700000| Untracked 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| O|  |TAMS 0x000000008a700000, 0x000000008a800000| Untracked 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| O|  |TAMS 0x000000008a800000, 0x000000008a900000| Untracked 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| O|  |TAMS 0x000000008a900000, 0x000000008aa00000| Untracked 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008aa00000, 0x000000008ab00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%|HS|  |TAMS 0x000000008ab00000, 0x000000008ac00000| Complete 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| O|  |TAMS 0x000000008ac00000, 0x000000008ad00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| O|  |TAMS 0x000000008ad00000, 0x000000008ae00000| Untracked 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| O|  |TAMS 0x000000008ae00000, 0x000000008af00000| Untracked 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| O|  |TAMS 0x000000008af00000, 0x000000008b000000| Untracked 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| O|  |TAMS 0x000000008b000000, 0x000000008b100000| Untracked 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| O|  |TAMS 0x000000008b100000, 0x000000008b200000| Untracked 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| O|  |TAMS 0x000000008b200000, 0x000000008b300000| Untracked 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| O|  |TAMS 0x000000008b300000, 0x000000008b400000| Untracked 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| O|  |TAMS 0x000000008b400000, 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| O|  |TAMS 0x000000008b500000, 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%| O|  |TAMS 0x000000008b600000, 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000, 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000, 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000, 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000, 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| O|  |TAMS 0x000000008bb00000, 0x000000008bc00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| O|  |TAMS 0x000000008bc00000, 0x000000008bd00000| Untracked 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| O|  |TAMS 0x000000008bd00000, 0x000000008be00000| Untracked 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| O|  |TAMS 0x000000008be00000, 0x000000008bf00000| Untracked 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| O|  |TAMS 0x000000008bf00000, 0x000000008c000000| Untracked 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| O|  |TAMS 0x000000008c000000, 0x000000008c100000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000, 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| O|  |TAMS 0x000000008c200000, 0x000000008c300000| Untracked 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| O|  |TAMS 0x000000008c300000, 0x000000008c400000| Untracked 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| O|  |TAMS 0x000000008c400000, 0x000000008c4ae000| Untracked 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| O|  |TAMS 0x000000008c500000, 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| O|  |TAMS 0x000000008c600000, 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| O|  |TAMS 0x000000008c700000, 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| O|  |TAMS 0x000000008c800000, 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| O|  |TAMS 0x000000008c900000, 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%|HS|  |TAMS 0x000000008ca00000, 0x000000008ca00000| Complete 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| O|  |TAMS 0x000000008cb00000, 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| O|  |TAMS 0x000000008cc00000, 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| O|  |TAMS 0x000000008cd00000, 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| O|  |TAMS 0x000000008ce00000, 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| O|  |TAMS 0x000000008cf00000, 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d000000, 0x000000008d100000|  0%| F|  |TAMS 0x000000008d000000, 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d100000, 0x000000008d200000|  0%| F|  |TAMS 0x000000008d100000, 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d200000, 0x000000008d300000|  0%| F|  |TAMS 0x000000008d200000, 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d300000, 0x000000008d400000|  0%| F|  |TAMS 0x000000008d300000, 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d400000, 0x000000008d500000|  0%| F|  |TAMS 0x000000008d400000, 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d500000, 0x000000008d600000|  0%| F|  |TAMS 0x000000008d500000, 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d600000, 0x000000008d700000|  0%| F|  |TAMS 0x000000008d600000, 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d700000, 0x000000008d800000|  0%| F|  |TAMS 0x000000008d700000, 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d800000, 0x000000008d900000|  0%| F|  |TAMS 0x000000008d800000, 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008d900000, 0x000000008da00000|  0%| F|  |TAMS 0x000000008d900000, 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008da00000, 0x000000008db00000|  0%| F|  |TAMS 0x000000008da00000, 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008db00000, 0x000000008dc00000|  0%| F|  |TAMS 0x000000008db00000, 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dc00000, 0x000000008dd00000|  0%| F|  |TAMS 0x000000008dc00000, 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008dd00000, 0x000000008de00000|  0%| F|  |TAMS 0x000000008dd00000, 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008de00000, 0x000000008df00000|  0%| F|  |TAMS 0x000000008de00000, 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008df00000, 0x000000008e000000|  0%| F|  |TAMS 0x000000008df00000, 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e000000, 0x000000008e100000|  0%| F|  |TAMS 0x000000008e000000, 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e100000, 0x000000008e200000|  0%| F|  |TAMS 0x000000008e100000, 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e200000, 0x000000008e300000|  0%| F|  |TAMS 0x000000008e200000, 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e300000, 0x000000008e400000|  0%| F|  |TAMS 0x000000008e300000, 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e400000, 0x000000008e500000|  0%| F|  |TAMS 0x000000008e400000, 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e500000, 0x000000008e600000|  0%| F|  |TAMS 0x000000008e500000, 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e600000, 0x000000008e700000|  0%| F|  |TAMS 0x000000008e600000, 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e700000, 0x000000008e800000|  0%| F|  |TAMS 0x000000008e700000, 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e800000, 0x000000008e900000|  0%| F|  |TAMS 0x000000008e800000, 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008e900000, 0x000000008ea00000|  0%| F|  |TAMS 0x000000008e900000, 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008ea00000, 0x000000008eb00000|  0%| F|  |TAMS 0x000000008ea00000, 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008eb00000, 0x000000008ec00000|  0%| F|  |TAMS 0x000000008eb00000, 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ec00000, 0x000000008ed00000|  0%| F|  |TAMS 0x000000008ec00000, 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ed00000, 0x000000008ee00000|  0%| F|  |TAMS 0x000000008ed00000, 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| S|CS|TAMS 0x000000008ee00000, 0x000000008ee00000| Complete 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| S|CS|TAMS 0x000000008ef00000, 0x000000008ef00000| Complete 
| 240|0x000000008f000000, 0x000000008f000000, 0x000000008f100000|  0%| F|  |TAMS 0x000000008f000000, 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f100000, 0x000000008f200000|  0%| F|  |TAMS 0x000000008f100000, 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f200000, 0x000000008f300000|  0%| F|  |TAMS 0x000000008f200000, 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f300000, 0x000000008f400000|  0%| F|  |TAMS 0x000000008f300000, 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f400000, 0x000000008f500000|  0%| F|  |TAMS 0x000000008f400000, 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f500000, 0x000000008f600000|  0%| F|  |TAMS 0x000000008f500000, 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f600000, 0x000000008f700000|  0%| F|  |TAMS 0x000000008f600000, 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f700000, 0x000000008f800000|  0%| F|  |TAMS 0x000000008f700000, 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f800000, 0x000000008f900000|  0%| F|  |TAMS 0x000000008f800000, 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008f900000, 0x000000008fa00000|  0%| F|  |TAMS 0x000000008f900000, 0x000000008f900000| Untracked 
| 250|0x000000008fa00000, 0x000000008fa00000, 0x000000008fb00000|  0%| F|  |TAMS 0x000000008fa00000, 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fb00000, 0x000000008fc00000|  0%| F|  |TAMS 0x000000008fb00000, 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fc00000, 0x000000008fd00000|  0%| F|  |TAMS 0x000000008fc00000, 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fd00000, 0x000000008fe00000|  0%| F|  |TAMS 0x000000008fd00000, 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008fe00000, 0x000000008ff00000|  0%| F|  |TAMS 0x000000008fe00000, 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x000000008ff00000, 0x0000000090000000|  0%| F|  |TAMS 0x000000008ff00000, 0x000000008ff00000| Untracked 

Card table byte_map: [0x00000257785e0000,0x00000257789e0000] _byte_map_base: 0x00000257781e0000

Marking Bits (Prev, Next): (CMBitMap*) 0x000002575d94c800, (CMBitMap*) 0x000002575d94c7c0
 Prev Bits: [0x000002577ade0000, 0x000002577cde0000)
 Next Bits: [0x0000025778de0000, 0x000002577ade0000)

Polling page: 0x000002575f190000

Metaspace:

Usage:
  Non-class:    136.79 MB used.
      Class:     20.05 MB used.
       Both:    156.84 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     137.31 MB ( 72%) committed,  3 nodes.
      Class space:        1.00 GB reserved,      20.56 MB (  2%) committed,  1 nodes.
             Both:        1.19 GB reserved,     157.88 MB ( 13%) committed. 

Chunk freelists:
   Non-Class:  6.69 MB
       Class:  11.33 MB
        Both:  18.02 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 192.75 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 1596.
num_arena_deaths: 8.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 2524.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 28.
num_chunks_taken_from_freelist: 8048.
num_chunk_merges: 12.
num_chunk_splits: 5785.
num_chunks_enlarged: 4343.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=13966Kb max_used=13966Kb free=105201Kb
 bounds [0x0000025770500000, 0x00000257712b0000, 0x0000025777960000]
CodeHeap 'profiled nmethods': size=119104Kb used=48837Kb max_used=48898Kb free=70266Kb
 bounds [0x0000025768960000, 0x000002576b930000, 0x000002576fdb0000]
CodeHeap 'non-nmethods': size=7488Kb used=4312Kb max_used=4405Kb free=3175Kb
 bounds [0x000002576fdb0000, 0x0000025770210000, 0x0000025770500000]
 total_blobs=22129 nmethods=20937 adapters=1098
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 21.962 Thread 0x0000025741080b50 24146       3       org.jetbrains.kotlin.com.intellij.util.containers.SLRUMap::put (52 bytes)
Event: 21.962 Thread 0x000002577f6dd2e0 24147       3       org.jetbrains.kotlin.com.intellij.util.containers.SLRUMap$$Lambda$1040/0x0000025701920aa8::check (13 bytes)
Event: 21.962 Thread 0x000002574f4ce2b0 24148       3       org.jetbrains.kotlin.com.intellij.util.containers.SLRUMap::lambda$new$0 (19 bytes)
Event: 21.962 Thread 0x000002574f4d4250 24149   !   3       org.jetbrains.kotlin.com.intellij.util.io.FileAccessorCache::disposeInvalidAccessors (100 bytes)
Event: 21.962 Thread 0x000002574f4ce2b0 nmethod 24148 0x0000025769f86d10 code [0x0000025769f86ec0, 0x0000025769f87108]
Event: 21.962 Thread 0x000002577f6dd2e0 nmethod 24147 0x000002576949e310 code [0x000002576949e4c0, 0x000002576949e748]
Event: 21.962 Thread 0x0000025741080b50 nmethod 24146 0x000002576a08ff90 code [0x000002576a0901a0, 0x000002576a090958]
Event: 21.962 Thread 0x000002574f4ce2b0 24150       3       org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ZipHandler$1::createAccessor (9 bytes)
Event: 21.962 Thread 0x000002577f6dd2e0 24151       3       org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ZipHandler$1::createAccessor (53 bytes)
Event: 21.962 Thread 0x0000025741080b50 24152       3       org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ZipHandler::access$002 (7 bytes)
Event: 21.962 Thread 0x000002574df8ade0 24154       4       sun.nio.fs.WindowsFileAttributes::fromFileAttributeData (104 bytes)
Event: 21.962 Thread 0x000002574f4ce2b0 nmethod 24150 0x000002576a51e790 code [0x000002576a51e940, 0x000002576a51eb88]
Event: 21.962 Thread 0x000002574f4ce2b0 24153       3       org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ZipHandler::access$102 (7 bytes)
Event: 21.962 Thread 0x0000025741080b50 nmethod 24152 0x0000025768e82810 code [0x0000025768e829a0, 0x0000025768e82ab8]
Event: 21.962 Thread 0x0000025741080b50 24155       3       org.jetbrains.kotlin.com.intellij.util.io.FileAccessorCache$Handle::<init> (43 bytes)
Event: 21.963 Thread 0x000002574f4d4250 nmethod 24149 0x000002576b547c90 code [0x000002576b547f00, 0x000002576b548d08]
Event: 21.963 Thread 0x000002574f4ce2b0 nmethod 24153 0x000002576a51e410 code [0x000002576a51e5a0, 0x000002576a51e6b8]
Event: 21.963 Thread 0x000002574f4d4250 24156       3       org.jetbrains.kotlin.com.intellij.util.io.ResourceHandle::<init> (5 bytes)
Event: 21.963 Thread 0x000002574f4d4250 nmethod 24156 0x000002576a51e090 code [0x000002576a51e220, 0x000002576a51e378]
Event: 21.963 Thread 0x000002577f6dd2e0 nmethod 24151 0x000002576b546a10 code [0x000002576b546c80, 0x000002576b5476c8]

GC Heap History (20 events):
Event: 19.481 GC heap after
{Heap after GC invocations=59 (full 0):
 garbage-first heap   total 256000K, used 176521K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 153140K, committed 154176K, reserved 1245184K
  class space    used 19547K, committed 20032K, reserved 1048576K
}
Event: 19.789 GC heap before
{Heap before GC invocations=59 (full 0):
 garbage-first heap   total 256000K, used 220553K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 49 young (50176K), 6 survivors (6144K)
 Metaspace       used 155528K, committed 156544K, reserved 1245184K
  class space    used 19908K, committed 20416K, reserved 1048576K
}
Event: 19.793 GC heap after
{Heap after GC invocations=60 (full 0):
 garbage-first heap   total 256000K, used 179186K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 155528K, committed 156544K, reserved 1245184K
  class space    used 19908K, committed 20416K, reserved 1048576K
}
Event: 20.114 GC heap before
{Heap before GC invocations=60 (full 0):
 garbage-first heap   total 256000K, used 221170K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 47 young (48128K), 6 survivors (6144K)
 Metaspace       used 159272K, committed 160320K, reserved 1245184K
  class space    used 20422K, committed 20928K, reserved 1048576K
}
Event: 20.116 GC heap after
{Heap after GC invocations=61 (full 0):
 garbage-first heap   total 256000K, used 181208K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 159272K, committed 160320K, reserved 1245184K
  class space    used 20422K, committed 20928K, reserved 1048576K
}
Event: 21.090 GC heap before
{Heap before GC invocations=61 (full 0):
 garbage-first heap   total 256000K, used 222168K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 45 young (46080K), 5 survivors (5120K)
 Metaspace       used 160026K, committed 161152K, reserved 1245184K
  class space    used 20506K, committed 21056K, reserved 1048576K
}
Event: 21.092 GC heap after
{Heap after GC invocations=62 (full 0):
 garbage-first heap   total 256000K, used 183300K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 160026K, committed 161152K, reserved 1245184K
  class space    used 20506K, committed 21056K, reserved 1048576K
}
Event: 21.577 GC heap before
{Heap before GC invocations=62 (full 0):
 garbage-first heap   total 256000K, used 222212K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 43 young (44032K), 5 survivors (5120K)
 Metaspace       used 160376K, committed 161472K, reserved 1245184K
  class space    used 20511K, committed 21056K, reserved 1048576K
}
Event: 21.581 GC heap after
{Heap after GC invocations=63 (full 0):
 garbage-first heap   total 256000K, used 186551K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 160376K, committed 161472K, reserved 1245184K
  class space    used 20511K, committed 21056K, reserved 1048576K
}
Event: 21.753 GC heap before
{Heap before GC invocations=63 (full 0):
 garbage-first heap   total 256000K, used 221367K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 41 young (41984K), 6 survivors (6144K)
 Metaspace       used 160569K, committed 161664K, reserved 1245184K
  class space    used 20526K, committed 21056K, reserved 1048576K
}
Event: 21.755 GC heap after
{Heap after GC invocations=64 (full 0):
 garbage-first heap   total 256000K, used 190104K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 160569K, committed 161664K, reserved 1245184K
  class space    used 20526K, committed 21056K, reserved 1048576K
}
Event: 21.859 GC heap before
{Heap before GC invocations=64 (full 0):
 garbage-first heap   total 256000K, used 205464K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 6 survivors (6144K)
 Metaspace       used 160596K, committed 161664K, reserved 1245184K
  class space    used 20527K, committed 21056K, reserved 1048576K
}
Event: 21.861 GC heap after
{Heap after GC invocations=65 (full 0):
 garbage-first heap   total 256000K, used 192042K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 160596K, committed 161664K, reserved 1245184K
  class space    used 20527K, committed 21056K, reserved 1048576K
}
Event: 21.919 GC heap before
{Heap before GC invocations=65 (full 0):
 garbage-first heap   total 256000K, used 225834K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 28 young (28672K), 2 survivors (2048K)
 Metaspace       used 160598K, committed 161664K, reserved 1245184K
  class space    used 20527K, committed 21056K, reserved 1048576K
}
Event: 21.921 GC heap after
{Heap after GC invocations=66 (full 0):
 garbage-first heap   total 256000K, used 204421K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 160598K, committed 161664K, reserved 1245184K
  class space    used 20527K, committed 21056K, reserved 1048576K
}
Event: 21.931 GC heap before
{Heap before GC invocations=66 (full 0):
 garbage-first heap   total 256000K, used 209541K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 4 survivors (4096K)
 Metaspace       used 160598K, committed 161664K, reserved 1245184K
  class space    used 20527K, committed 21056K, reserved 1048576K
}
Event: 21.933 GC heap after
{Heap after GC invocations=67 (full 0):
 garbage-first heap   total 256000K, used 209754K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 160598K, committed 161664K, reserved 1245184K
  class space    used 20527K, committed 21056K, reserved 1048576K
}
Event: 21.948 GC heap before
{Heap before GC invocations=67 (full 0):
 garbage-first heap   total 256000K, used 219994K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 3 survivors (3072K)
 Metaspace       used 160598K, committed 161664K, reserved 1245184K
  class space    used 20527K, committed 21056K, reserved 1048576K
}
Event: 21.950 GC heap after
{Heap after GC invocations=68 (full 0):
 garbage-first heap   total 256000K, used 206865K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 160598K, committed 161664K, reserved 1245184K
  class space    used 20527K, committed 21056K, reserved 1048576K
}
Event: 21.963 GC heap before
{Heap before GC invocations=68 (full 0):
 garbage-first heap   total 256000K, used 217105K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 2 survivors (2048K)
 Metaspace       used 160604K, committed 161664K, reserved 1245184K
  class space    used 20527K, committed 21056K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.008 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\java.dll
Event: 0.018 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\jsvml.dll
Event: 0.045 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\zip.dll
Event: 0.047 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\instrument.dll
Event: 0.051 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\net.dll
Event: 0.052 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\nio.dll
Event: 0.053 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\zip.dll
Event: 0.156 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\jimage.dll
Event: 0.233 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\verify.dll
Event: 0.351 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 0.354 Loaded shared library C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 0.756 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\management.dll
Event: 0.759 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\management_ext.dll
Event: 0.881 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\extnet.dll
Event: 0.979 Loaded shared library D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 21.885 Thread 0x0000025741157cb0 DEOPT PACKING pc=0x0000025770bfc91c sp=0x0000006b4bbf62d0
Event: 21.885 Thread 0x0000025741157cb0 DEOPT UNPACKING pc=0x000002576fe069a3 sp=0x0000006b4bbf6230 mode 2
Event: 21.886 Thread 0x0000025741157cb0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000025770bfb31c relative=0x000000000000039c
Event: 21.886 Thread 0x0000025741157cb0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000025770bfb31c method=org.jetbrains.kotlin.com.intellij.openapi.progress.ProgressManager.checkCanceled()V @ 5 c2
Event: 21.886 Thread 0x0000025741157cb0 DEOPT PACKING pc=0x0000025770bfb31c sp=0x0000006b4bbf5fc0
Event: 21.886 Thread 0x0000025741157cb0 DEOPT UNPACKING pc=0x000002576fe069a3 sp=0x0000006b4bbf5f78 mode 2
Event: 21.887 Thread 0x0000025741157cb0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000025770f40238 relative=0x0000000000000ff8
Event: 21.887 Thread 0x0000025741157cb0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000025770f40238 method=org.jetbrains.kotlin.com.intellij.openapi.progress.ProgressManager.checkCanceled()V @ 5 c2
Event: 21.887 Thread 0x0000025741157cb0 DEOPT PACKING pc=0x0000025770f40238 sp=0x0000006b4bbf5870
Event: 21.887 Thread 0x0000025741157cb0 DEOPT UNPACKING pc=0x000002576fe069a3 sp=0x0000006b4bbf57b0 mode 2
Event: 21.888 Thread 0x0000025741157cb0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000025770844e38 relative=0x0000000000000418
Event: 21.888 Thread 0x0000025741157cb0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000025770844e38 method=jdk.internal.jrtfs.JrtPath.normalize(Ljava/lang/String;)Ljava/lang/String; @ 6 c2
Event: 21.888 Thread 0x0000025741157cb0 DEOPT PACKING pc=0x0000025770844e38 sp=0x0000006b4bbf60d0
Event: 21.888 Thread 0x0000025741157cb0 DEOPT UNPACKING pc=0x000002576fe069a3 sp=0x0000006b4bbf6058 mode 2
Event: 21.905 Thread 0x0000025741157cb0 DEOPT PACKING pc=0x0000025769608715 sp=0x0000006b4bbf5e60
Event: 21.905 Thread 0x0000025741157cb0 DEOPT UNPACKING pc=0x000002576fe07143 sp=0x0000006b4bbf53b0 mode 0
Event: 21.918 Thread 0x0000025741157cb0 DEOPT PACKING pc=0x00000257695efa3a sp=0x0000006b4bbf5f00
Event: 21.918 Thread 0x0000025741157cb0 DEOPT UNPACKING pc=0x000002576fe07143 sp=0x0000006b4bbf53e0 mode 0
Event: 21.957 Thread 0x0000025741157cb0 DEOPT PACKING pc=0x000002576960617c sp=0x0000006b4bbf6080
Event: 21.957 Thread 0x0000025741157cb0 DEOPT UNPACKING pc=0x000002576fe07143 sp=0x0000006b4bbf55d0 mode 0

Classes loaded (20 events):
Event: 20.977 Loading class java/net/Collections
Event: 20.977 Loading class java/net/Collections done
Event: 20.989 Loading class java/lang/Map
Event: 20.989 Loading class java/lang/Map done
Event: 20.991 Loading class java/io/Map
Event: 20.991 Loading class java/io/Map done
Event: 20.992 Loading class java/net/Map
Event: 20.992 Loading class java/net/Map done
Event: 20.995 Loading class java/lang/HashMap
Event: 20.995 Loading class java/lang/HashMap done
Event: 20.997 Loading class java/io/HashMap
Event: 20.997 Loading class java/io/HashMap done
Event: 20.999 Loading class java/net/HashMap
Event: 20.999 Loading class java/net/HashMap done
Event: 21.014 Loading class java/lang/LinkedList
Event: 21.014 Loading class java/lang/LinkedList done
Event: 21.014 Loading class java/io/LinkedList
Event: 21.014 Loading class java/io/LinkedList done
Event: 21.015 Loading class java/net/LinkedList
Event: 21.015 Loading class java/net/LinkedList done

Classes unloaded (15 events):
Event: 4.461 Thread 0x000002577f6ad7e0 Unloading class 0x00000257015983e8 '_BuildScript_$_run_closure1$_closure2'
Event: 4.461 Thread 0x000002577f6ad7e0 Unloading class 0x0000025701598000 '_BuildScript_$_run_closure1'
Event: 4.461 Thread 0x000002577f6ad7e0 Unloading class 0x0000025701588800 '_BuildScript_'
Event: 6.103 Thread 0x000002577f6ad7e0 Unloading class 0x000002570163a800 '_BuildScript_'
Event: 6.103 Thread 0x000002577f6ad7e0 Unloading class 0x0000025701638918 '_BuildScript_$_run_closure1$_closure2'
Event: 6.103 Thread 0x000002577f6ad7e0 Unloading class 0x0000025701638530 '_BuildScript_$_run_closure1'
Event: 6.103 Thread 0x000002577f6ad7e0 Unloading class 0x0000025701638000 '_BuildScript_'
Event: 6.103 Thread 0x000002577f6ad7e0 Unloading class 0x0000025701630d00 '_BuildScript_$_run_closure1$_closure2'
Event: 6.103 Thread 0x000002577f6ad7e0 Unloading class 0x0000025701630918 '_BuildScript_$_run_closure1'
Event: 6.103 Thread 0x000002577f6ad7e0 Unloading class 0x00000257016303e8 '_BuildScript_'
Event: 6.103 Thread 0x000002577f6ad7e0 Unloading class 0x0000025701630000 'ProtobufPatchPlugin$_apply_closure1$_closure2$_closure3$_closure4'
Event: 6.103 Thread 0x000002577f6ad7e0 Unloading class 0x00000257015983e8 'ProtobufPatchPlugin$_apply_closure1$_closure2$_closure3'
Event: 6.103 Thread 0x000002577f6ad7e0 Unloading class 0x0000025701598000 'ProtobufPatchPlugin$_apply_closure1$_closure2'
Event: 6.103 Thread 0x000002577f6ad7e0 Unloading class 0x0000025701588ac8 'ProtobufPatchPlugin$_apply_closure1'
Event: 6.103 Thread 0x000002577f6ad7e0 Unloading class 0x0000025701588800 'ProtobufPatchPlugin'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 21.062 Thread 0x0000025741157cb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d37c7d8}> (0x000000008d37c7d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 21.063 Thread 0x0000025741157cb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d38f460}> (0x000000008d38f460) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 21.064 Thread 0x0000025741157cb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d399f80}> (0x000000008d399f80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 21.082 Thread 0x0000025741157cb0 Exception <a 'java/lang/ClassNotFoundException'{0x000000008d2ba3f8}: init_tzdylnphs6le8hnnb8mi5eh6BeanInfo> (0x000000008d2ba3f8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 21.082 Thread 0x0000025741157cb0 Exception <a 'java/lang/ClassNotFoundException'{0x000000008d2c0978}: init_tzdylnphs6le8hnnb8mi5eh6Customizer> (0x000000008d2c0978) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 21.089 Thread 0x0000025741157cb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d2fdba0}> (0x000000008d2fdba0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 21.090 Thread 0x0000025741157cb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d2ff060}> (0x000000008d2ff060) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 21.093 Thread 0x0000025741157cb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008ff00818}> (0x000000008ff00818) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 21.093 Thread 0x0000025741157cb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008ff018b8}> (0x000000008ff018b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 21.132 Thread 0x0000025741157cb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008e4a7cd8}> (0x000000008e4a7cd8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 21.132 Thread 0x0000025741157cb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008e4a9320}> (0x000000008e4a9320) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 21.133 Thread 0x0000025741157cb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008e4ac6d8}> (0x000000008e4ac6d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 21.623 Thread 0x0000025741157cb0 Exception <a 'java/lang/ClassNotFoundException'{0x000000008ecc0668}: GradleAnnotationProcessorPatchPluginBeanInfo> (0x000000008ecc0668) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 21.623 Thread 0x0000025741157cb0 Exception <a 'java/lang/ClassNotFoundException'{0x000000008eccadc8}: GradleAnnotationProcessorPatchPluginCustomizer> (0x000000008eccadc8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 21.628 Thread 0x0000025741157cb0 Exception <a 'java/lang/ClassNotFoundException'{0x000000008eaa2e60}: GradleAnnotationProcessorPatchPlugin$AnnotationProcessorModelBuilderBeanInfo> (0x000000008eaa2e60) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 21.629 Thread 0x0000025741157cb0 Exception <a 'java/lang/ClassNotFoundException'{0x000000008eaad810}: GradleAnnotationProcessorPatchPlugin$AnnotationProcessorModelBuilderCustomizer> (0x000000008eaad810) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 21.635 Thread 0x0000025741157cb0 Exception <a 'java/lang/ClassNotFoundException'{0x000000008e80f528}: org/gradle/tooling/provider/model/internal/DefaultToolingModelBuilderRegistryBeanInfo> (0x000000008e80f528) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 21.637 Thread 0x0000025741157cb0 Exception <a 'java/lang/ClassNotFoundException'{0x000000008e824990}: org/gradle/tooling/provider/model/internal/DefaultToolingModelBuilderRegistryCustomizer> (0x000000008e824990) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 21.780 Thread 0x0000025741157cb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008f4d4ce0}> (0x000000008f4d4ce0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 21.781 Thread 0x0000025741157cb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008f4d67d8}> (0x000000008f4d67d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]

VM Operations (20 events):
Event: 21.092 Executing VM operation: G1CollectForAllocation done
Event: 21.415 Executing VM operation: ICBufferFull
Event: 21.416 Executing VM operation: ICBufferFull done
Event: 21.577 Executing VM operation: G1CollectForAllocation
Event: 21.581 Executing VM operation: G1CollectForAllocation done
Event: 21.688 Executing VM operation: ICBufferFull
Event: 21.689 Executing VM operation: ICBufferFull done
Event: 21.753 Executing VM operation: G1CollectForAllocation
Event: 21.755 Executing VM operation: G1CollectForAllocation done
Event: 21.859 Executing VM operation: G1TryInitiateConcMark
Event: 21.861 Executing VM operation: G1TryInitiateConcMark done
Event: 21.904 Executing VM operation: HandshakeAllThreads
Event: 21.904 Executing VM operation: HandshakeAllThreads done
Event: 21.919 Executing VM operation: G1CollectForAllocation
Event: 21.921 Executing VM operation: G1CollectForAllocation done
Event: 21.931 Executing VM operation: G1TryInitiateConcMark
Event: 21.933 Executing VM operation: G1TryInitiateConcMark done
Event: 21.948 Executing VM operation: G1CollectForAllocation
Event: 21.950 Executing VM operation: G1CollectForAllocation done
Event: 21.963 Executing VM operation: G1CollectForAllocation

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 20.066 Thread 0x000002577f6eb840 flushing  nmethod 0x0000025769c97710
Event: 20.066 Thread 0x000002577f6eb840 flushing  nmethod 0x0000025769ce2710
Event: 20.071 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576a081810
Event: 20.071 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576a0c8410
Event: 20.071 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576a101d10
Event: 20.071 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576a115590
Event: 20.071 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576a115c90
Event: 20.071 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576a11c590
Event: 20.071 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576a11c990
Event: 20.074 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576a7c9610
Event: 20.081 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576b541290
Event: 20.081 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576b543790
Event: 21.909 Thread 0x000002577f6eb840 flushing  nmethod 0x0000025768b86610
Event: 21.912 Thread 0x000002577f6eb840 flushing  nmethod 0x0000025768e82810
Event: 21.915 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576949e310
Event: 21.915 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576957c090
Event: 21.922 Thread 0x000002577f6eb840 flushing  nmethod 0x0000025769f86d10
Event: 21.923 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576a08ff90
Event: 21.925 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576a51d790
Event: 21.936 Thread 0x000002577f6eb840 flushing  nmethod 0x000002576b546a10

Events (20 events):
Event: 21.471 Thread 0x0000025741157cb0 Thread added: 0x000002574e074870
Event: 21.471 Thread 0x0000025741157cb0 Thread added: 0x000002574e075cb0
Event: 21.472 Thread 0x0000025741157cb0 Thread added: 0x000002574e0770f0
Event: 21.472 Thread 0x0000025741157cb0 Thread added: 0x000002574e074360
Event: 21.472 Thread 0x0000025741157cb0 Thread added: 0x000002574e0761c0
Event: 21.597 Thread 0x0000025741157cb0 Thread added: 0x000002574e073e50
Event: 21.597 Thread 0x0000025741157cb0 Thread added: 0x000002574e0766d0
Event: 21.598 Thread 0x0000025741157cb0 Thread added: 0x000002574e076be0
Event: 21.599 Thread 0x0000025741157cb0 Thread added: 0x000002574e073940
Event: 21.599 Thread 0x0000025741157cb0 Thread added: 0x000002574e077b10
Event: 21.600 Thread 0x0000025741157cb0 Thread added: 0x000002574c523a80
Event: 21.600 Thread 0x0000025741157cb0 Thread added: 0x000002574c527c50
Event: 21.601 Thread 0x0000025741157cb0 Thread added: 0x000002574c52a9e0
Event: 21.601 Thread 0x0000025741157cb0 Thread added: 0x000002574c528670
Event: 21.602 Thread 0x0000025741157cb0 Thread added: 0x000002574c528160
Event: 21.602 Thread 0x0000025741157cb0 Thread added: 0x000002574c528b80
Event: 21.603 Thread 0x0000025741157cb0 Thread added: 0x000002574c5295a0
Event: 21.603 Thread 0x0000025741157cb0 Thread added: 0x000002574c529090
Event: 21.604 Thread 0x0000025741157cb0 Thread added: 0x000002574c529ab0
Event: 21.604 Thread 0x0000025741157cb0 Thread added: 0x000002574c529fc0


Dynamic libraries:
0x00007ff6cd690000 - 0x00007ff6cd69e000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\java.exe
0x00007ffc03880000 - 0x00007ffc03ae6000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffc01e70000 - 0x00007ffc01f39000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffc00d70000 - 0x00007ffc0113c000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffc01140000 - 0x00007ffc0128b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffbec1c0000 - 0x00007ffbec1d7000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\jli.dll
0x00007ffbece80000 - 0x00007ffbece9b000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\VCRUNTIME140.dll
0x00007ffc01f50000 - 0x00007ffc0211a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffc014a0000 - 0x00007ffc014c7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffbe4850000 - 0x00007ffbe4aea000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffc022f0000 - 0x00007ffc0231b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffc017b0000 - 0x00007ffc01859000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffc00c30000 - 0x00007ffc00d62000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffc01590000 - 0x00007ffc01633000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffc033b0000 - 0x00007ffc033e0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffbed590000 - 0x00007ffbed59c000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\vcruntime140_1.dll
0x00007ffba8df0000 - 0x00007ffba8e7d000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\msvcp140.dll
0x00007ffb70b80000 - 0x00007ffb717ec000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\server\jvm.dll
0x00007ffc033f0000 - 0x00007ffc034a2000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffc02240000 - 0x00007ffc022e6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffc02120000 - 0x00007ffc02236000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffc01df0000 - 0x00007ffc01e64000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffbff640000 - 0x00007ffbff69e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffbf9fe0000 - 0x00007ffbfa016000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffbfaf60000 - 0x00007ffbfaf6b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffbff620000 - 0x00007ffbff634000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffbff8f0000 - 0x00007ffbff90a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffbed020000 - 0x00007ffbed02a000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\jimage.dll
0x00007ffbf8df0000 - 0x00007ffbf9031000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffc023d0000 - 0x00007ffc02754000 	C:\WINDOWS\System32\combase.dll
0x00007ffc02e90000 - 0x00007ffc02f70000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffbf11b0000 - 0x00007ffbf11e9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffc00a10000 - 0x00007ffc00aa9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffbecd50000 - 0x00007ffbecd5e000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\instrument.dll
0x00007ffbac6e0000 - 0x00007ffbac705000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\java.dll
0x00007ffb90f30000 - 0x00007ffb91006000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\jsvml.dll
0x00007ffc02760000 - 0x00007ffc02e8d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffc00ab0000 - 0x00007ffc00c24000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffbfe6b0000 - 0x00007ffbfef06000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffc01860000 - 0x00007ffc0194f000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffc03340000 - 0x00007ffc033a9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffc00920000 - 0x00007ffc0094f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffbb3250000 - 0x00007ffbb3268000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\zip.dll
0x00007ffbb2b10000 - 0x00007ffbb2b29000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\net.dll
0x00007ffbf9320000 - 0x00007ffbf943e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffbffe60000 - 0x00007ffbffeca000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffbb2740000 - 0x00007ffbb2756000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\nio.dll
0x00007ffbecb60000 - 0x00007ffbecb70000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\verify.dll
0x00007ffbda3e0000 - 0x00007ffbda407000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffbc5b70000 - 0x00007ffbc5cb4000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffbeac20000 - 0x00007ffbeac29000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\management.dll
0x00007ffbe5ef0000 - 0x00007ffbe5efb000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\management_ext.dll
0x00007ffc01f40000 - 0x00007ffc01f48000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffc00100000 - 0x00007ffc0011c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffbff850000 - 0x00007ffbff88a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffbfff00000 - 0x00007ffbfff2b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffc008f0000 - 0x00007ffc00916000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffc00120000 - 0x00007ffc0012c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffbff270000 - 0x00007ffbff2a3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffc03280000 - 0x00007ffc0328a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffbf8d70000 - 0x00007ffbf8d8f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffbf8c90000 - 0x00007ffbf8cb5000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffbff2b0000 - 0x00007ffbff3d7000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffbe1e10000 - 0x00007ffbe1e18000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\extnet.dll
0x00007ffbe1d90000 - 0x00007ffbe1d9e000 	D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\sunmscapi.dll
0x00007ffc01320000 - 0x00007ffc01497000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffc00310000 - 0x00007ffc00340000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffc002c0000 - 0x00007ffc002ff000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffba8c80000 - 0x00007ffba8c88000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\gradle-daemon-main-8.11.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 268435456                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6
PATH=C:\Program Files\Google\Chrome\Application;C:\Program Files\PowerShell\7-preview;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\AndroidBuildEnv\SDK\platform-tools\;D:\AndroidBuildEnv\SDK\ndk\27.0.11902837\build\cmake\;D:\AndroidBuildEnv\gradle\gradle-8.11.1\bin\;D:\AndroidBuildEnv\jdk\openjdk-17.0.15+6\bin\;D:\AndroidBuildEnv\SDK\build-tools\;D:\AndroidBuildEnv\SDK\cmake\3.22.1\bin\;D:\AndroidBuildEnv\SDK\cmdline-tools\latest\bin\;D:\Programs\Git\cmd;C:\Program Files\PowerShell\7-preview\preview;D:\AndroidBuildEnv\Nodejs\;D:\ProgramData\ComposerSetup\bin\composer.bat;D:\AndroidBuildEnv\dotnet\;D:\AndroidBuildEnv\SDK\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Programs\Cursor\cursor\resources\app\bin;D:\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Program Files\PowerShell\7;D:\AndroidBuildEnv\php\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Programs\Cursor\cursor\resources\app\bin;D:\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\.lmstudio\bin;;C:\Users\<USER>\AppData\Local\Programs\Ollama
USERNAME=kothar
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 33 Stepping 0, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 0 days 6:23 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 25 model 33 stepping 0 microcode 0xa201016, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for all 16 processors :
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801

Memory: 4k page, system-wide physical 16307M (1165M free)
TotalPageFile size 31477M (AvailPageFile size 119M)
current process WorkingSet (physical memory assigned to process): 720M, peak: 734M
current process commit charge ("private bytes"): 775M, peak: 1019M

vm_info: OpenJDK 64-Bit Server VM (17.0.15+6-adhoc..jdk17u) for windows-amd64 JRE (17.0.15+6-adhoc..jdk17u), built on Apr 17 2025 09:38:37 by "" with MS VC++ 16.10 / 16.11 (VS2019)

END.
