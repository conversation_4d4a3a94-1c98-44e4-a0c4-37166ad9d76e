package com.bearmod.loader.ui.test;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bearmod.loader.R;
import com.bearmod.loader.auth.DirectKeyAuthManager;
import com.bearmod.loader.auth.AuthResult;
import com.bearmod.loader.databinding.ActivityApiTestBinding;
import com.bearmod.loader.ui.adapters.ApiTestResultAdapter;
import com.bearmod.loader.util.ApiConnectionTester;

import java.util.ArrayList;
import java.util.List;

/**
 * Activity for testing API connections
 */
public class ApiTestActivity extends AppCompatActivity {

    private ActivityApiTestBinding binding;
    private ApiTestResultAdapter adapter;
    private List<ApiTestResultAdapter.ApiTestResult> results = new ArrayList<>();
    private DirectKeyAuthManager keyAuthManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Initialize view binding
        binding = ActivityApiTestBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // Set up toolbar
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("API Connection Test");
        }

        // Set up RecyclerView
        adapter = new ApiTestResultAdapter(results);
        binding.recyclerView.setAdapter(adapter);
        binding.recyclerView.setLayoutManager(new LinearLayoutManager(this));
        binding.recyclerView.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));

        // Get KeyAuth manager instance
        keyAuthManager = DirectKeyAuthManager.getInstance();

        // Set up test connection button
        binding.btnTestConnection.setOnClickListener(v -> testApiConnections());

        // Set up test license button
        binding.btnTestLicense.setOnClickListener(v -> testLicenseValidation());
    }

    /**
     * Test API connections
     */
    private void testApiConnections() {
        // Clear previous results
        results.clear();
        adapter.notifyDataSetChanged();

        // Show progress
        binding.progressBar.setVisibility(View.VISIBLE);
        binding.tvStatus.setText("Testing API connections...");
        binding.tvStatus.setVisibility(View.VISIBLE);

        // Create connection tester
        ApiConnectionTester tester = new ApiConnectionTester();

        // Test all connections
        tester.testAllConnections(new ApiConnectionTester.ConnectionTestCallback() {
            @Override
            public void onResult(String url, boolean success, String message) {
                // Add result to list
                results.add(new ApiTestResultAdapter.ApiTestResult(url, success, message));
                adapter.notifyItemInserted(results.size() - 1);
            }

            @Override
            public void onComplete(boolean anySuccessful) {
                // Update UI
                binding.progressBar.setVisibility(View.GONE);
                
                if (anySuccessful) {
                    binding.tvStatus.setText("At least one API connection was successful");
                    binding.btnTestLicense.setEnabled(true);
                } else {
                    binding.tvStatus.setText("All API connections failed");
                    binding.btnTestLicense.setEnabled(false);
                }
            }
        });
    }

    /**
     * Test license validation
     */
    private void testLicenseValidation() {
        // Show progress
        binding.progressBar.setVisibility(View.VISIBLE);
        binding.tvStatus.setText("Testing license validation...");

        // Get license key from input
        String licenseKey = binding.etLicenseKey.getText().toString().trim();
        
        if (licenseKey.isEmpty()) {
            binding.tvStatus.setText("Please enter a license key");
            binding.progressBar.setVisibility(View.GONE);
            return;
        }

        // Test license validation
        keyAuthManager.login(licenseKey, new DirectKeyAuthManager.AuthCallback() {
            @Override
            public void onSuccess(AuthResult result) {
                // Update UI
                binding.progressBar.setVisibility(View.GONE);
                binding.tvStatus.setText("License validation successful: " + result.getMessage());
                
                // Add result to list
                results.add(new ApiTestResultAdapter.ApiTestResult(
                        "License Validation",
                        true,
                        "License valid until: " + result.getExpiryDate()
                ));
                adapter.notifyItemInserted(results.size() - 1);
            }

            @Override
            public void onError(String error) {
                // Update UI
                binding.progressBar.setVisibility(View.GONE);
                binding.tvStatus.setText("License validation failed: " + error);
                
                // Add result to list
                results.add(new ApiTestResultAdapter.ApiTestResult(
                        "License Validation",
                        false,
                        error
                ));
                adapter.notifyItemInserted(results.size() - 1);
            }
        });
    }
}
