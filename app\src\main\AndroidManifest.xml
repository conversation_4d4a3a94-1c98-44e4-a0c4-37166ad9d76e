<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Internet permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- Storage permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <!-- For Android 10+ storage access -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />

    <!-- Vibration permission -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- Media and video permissions -->
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application
        android:name=".BearLoaderApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.BearLoader"
        android:usesCleartextTraffic="true">

        <!-- Splash Activity -->
        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.BearLoader.Splash"
            android:windowSoftInputMode="adjustNothing"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Login Activity -->
        <activity
            android:name=".ui.auth.LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.BearLoader.NoActionBar" />

        <!-- Main Activity (Dashboard) -->
        <activity
            android:name=".ui.main.MainActivity"
            android:exported="false"
            android:theme="@style/Theme.BearLoader.NoActionBar" />

        <!-- Patch Execution Activity -->
        <activity
            android:name=".ui.patch.PatchExecutionActivity"
            android:exported="false"
            android:theme="@style/Theme.BearLoader.NoActionBar" />

        <!-- Download Activity -->
        <activity
            android:name=".ui.download.DownloadActivity"
            android:exported="false"
            android:theme="@style/Theme.BearLoader.NoActionBar" />

        <!-- Settings Activity -->
        <activity
            android:name=".ui.settings.SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.BearLoader.NoActionBar" />



    </application>

</manifest>