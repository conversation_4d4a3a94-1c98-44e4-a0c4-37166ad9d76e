# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.10.0"
  }
  digests {
    sha256: "#<\235g.+7sgw\303\234.:\213\377n0\367\334V1\270\273\327\265\351\217\263M!V"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.1.21"
  }
  digests {
    sha256: "&;\334g\236\037b\001-\267\260\221yby\266\327\034\363oG\227\251\217\361\254\340X5\362\001\310"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.1.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.1"
  }
  digests {
    sha256: "f\274,\023\275\221\373\216\240\236D[!\367\204\323\345\323\251\354\233\030\f\320~\016\222\b\302\3719\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.1"
  }
  digests {
    sha256: ",\'\336\031\2255gP\005U0fYzK \372\036\352|\"\212\264\357k2\265\3769\312\037Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.2.0"
  }
  digests {
    sha256: "H\0201Zy\206\220\265\323\273@\311+\315A\300E\375,6\273\374P\262|\272\303\326\240\355\241\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures-ktx"
    version: "1.2.0"
  }
  digests {
    sha256: "\341\363\341{\2645\214\315lw\312E\367\0065\311\253\2427&\037\031\352\244\366J\002\030\300\016*>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\324\":\346\250Dvw&}\377\"\206;mi\210\312h\264\322|\246\327K\201\004k\004yBW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.9.0"
  }
  digests {
    sha256: "7\330\233!\001\360t\254l&\t\027\332\273\030V\ad^\342\000\2520\030\307\305\275\347\016\334\361\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.9.0"
  }
  digests {
    sha256: "F8-\337\2724\215\271xF\317\250\035g\327#\004\334\025k\271b$5\213B\237\347H\274j\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.9.0"
  }
  digests {
    sha256: "d\371kR\307\b\225\200\1770 \342\350\372\021\363\354-\251\301W\366*\256!\242\311\323\264\204\247G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\362e\246e\201\236Q2+>\352*o\362\253\220\030l\255\361xz\201\335\213\357\f\351<J`\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.9.0"
  }
  digests {
    sha256: "\301\\\351r\314(\223jYj\234\322V\263/\"v\212a\325\335\201\017\245k\251ER*\016\347\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-ktx"
    version: "1.2.0"
  }
  digests {
    sha256: "\303?\234\275\223\036a\220\3128\252\t\277\212z\212\0319\035K\017\267\247`ZkY\362\324%\200\321"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.9.0"
  }
  digests {
    sha256: "\307\205\200\3128\315\024\344t28a\353\350\"\320L\004#\034\267\345\233$\336\200\024\006\245\0217j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.9.0"
  }
  digests {
    sha256: "\312\264\004\260]_\256\322\t\020J*\357\024\300\313\263\026\253\237\253\344\250\3607\260C\345\322\36445"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\372\235u\021\250\376\r^\334S\t\227\006\326O7b\035\317\353\237,Usi\355\203d_\310z#"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\3336x8qnU\206h#\306\336\355b\374\354#\374\344\207\277*\217\245e\243o\024\232\263\321\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.1"
  }
  digests {
    sha256: "\031\272P\320\224\3076\216\336\033L\317\021\225\316\270>5\227\a6Y?\202>Z\367\026\370\320]p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-android"
    version: "1.3.0"
  }
  digests {
    sha256: "!\vi\t\273\325\025\333\364\025\220\\\273\"\017\312\223\250\177\245F\203\217u@w\353\310\340H\205\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\360\255\336E\206ADGS\205\317J\247\340\267\376\262\177a\374\371G&e\355\230\314\227\033\006\261\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.3.0"
  }
  digests {
    sha256: "E\305\366A(@\365\r\347\277}\233\034J\214\201\352#h2Hz\0218\3132\v\214\231t\026\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.9.0"
  }
  digests {
    sha256: "3?\242\316m\267\n\303r\267\221\337\353\251\234\002\300\331{\032\213J\264\250MGbFm\022\262\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\257B\375\204\364\345+D\334\253\257\236\317\224\022\005S\222\233\301\240Y\276\262$\n\236\301\333\356\201O"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.1"
  }
  digests {
    sha256: "\237w\312\242\273\354|\356\372\\\223\036/\"<+8jYN\340D-\026\215\376\257\034\205\n\353\235"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.8.7"
  }
  digests {
    sha256: "`\"\210`\356\251\250\317\265@\004jnx\036\323\253\004o\314Z\377\301.\233\232\376TP\257\021\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.8.7"
  }
  digests {
    sha256: "X\037B\353||6\211&\016\275\366Fdr\004>/\221\222FfV@\022\316\vYtG\272O"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.38.0"
  }
  digests {
    sha256: "fa\3253P\220\245\374a\335\206\235 \225\274l\036!V\343\252G\246\344\253\253\337d\311\232x\211"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.2.1"
  }
  digests {
    sha256: "0\370\327\233x-(:\220\360\266\3676\221i\331\317V\000\221S\177\335`V\321\332\251\363\0037c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.1.1"
  }
  digests {
    sha256: "< T2\203(\203\036\266\346\233@\024\366\357\237\252\021\177\324\270\021\222\237:\221\323\3337_,\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.4.0"
  }
  digests {
    sha256: "\0172\314\257\374\332t\303\370\177\vL\376j\347\216\212\244\353:\327\335\244\362\355\360n\355\311G\245\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.1.0-beta02"
  }
  digests {
    sha256: "\272\372\303\312\231\036\326\212,|\246\3775)f\322\000\261.f\243B\321\017I|\270\026\217Y\005J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-fragment"
    version: "2.9.0"
  }
  digests {
    sha256: "&fu\2619\b\304ZIt\003C\3766.+k\240\241\364[q\332\340\301G\213\365\344\207O\354"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-android"
    version: "2.9.0"
  }
  digests {
    sha256: "]\257U\342\a\005\240G\036v\332\022\210p\30315\034Z\022<\316\364\244\262G~\271-7\331{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\367o\004\270O\310\233t\000\023\272\201\313\f\035\351\024\005=\325\\\362\330\360\306i\304\326\362\270\353\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-ui"
    version: "2.9.0"
  }
  digests {
    sha256: "\304\017_X\346\204=\216\017\367`\210\260\b\\\005\310\357\237X-\317\334\327$\213^P\232\016q>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0"
  }
  digests {
    sha256: "2\022\230[\344\022ss\312M\016\247\370\270\032%\n\342\020^\222Oy@\020]\006z\017\232\3010"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "3.0.0"
  }
  digests {
    sha256: "i\306\242\323E\033m\371T\232\223\372\267D\tN\277\a\244\260\316OE=l\217W^\360\376\311\241"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "3.0.0"
  }
  digests {
    sha256: "\255\2464\273\022\003\2207U\322\037\363\336\236\314\313\023\220a\374\340Z\246\030#E\324\311|6\236\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.13.1"
  }
  digests {
    sha256: "\224\205YB\324\231/\021)F\323\336\0343Np\2227\270\022m\2010\277\a\200|\001\212J! "
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.1.0"
  }
  digests {
    sha256: ",\347\220l\321\336\240Z\354\201\227]\262-T8#Y\300Z!\262Rz\330H\274`\366\262r\223"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.7.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime-android"
    version: "2.7.1"
  }
  digests {
    sha256: "\2062\236\327\031\b`\2177#N\331\t$\214i\2162\fh\005\310\257\350\364.}\326WDP&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.7.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common-jvm"
    version: "2.7.1"
  }
  digests {
    sha256: "\262\240\v\210\310\270\330\020\320~\313\337\222\375\377G\333\177\016\250\204\357\345\303\034\332zRl\233\032\345"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.7.1"
  }
  digests {
    sha256: "R\335\343\307\245tf!\205J\242\375@G1\\\375\032\274o\226\"YA\r\002H\245A\246\314\233"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.5.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-android"
    version: "2.5.0"
  }
  digests {
    sha256: "+\002\276:\355\215)\224\002\032\376\a!\205\257\224\342\224\"\377\350\006m\370\210h\357\254\241\026\036\360"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.5.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework-android"
    version: "2.5.0"
  }
  digests {
    sha256: "\205\r\347\003\331[\221\365\004wI\201\251\017\250)y\234\337\311\352\262\003i-$\240\304\r~q\347"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.16.0"
  }
  digests {
    sha256: "\211\201\034c\335&jHQ\375\033y\306\374\f\230)\226\354\3445\365\377H?\2700\312l\265<\324"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.16.0"
  }
  digests {
    sha256: "\225_\207*\364\322\243!\376\242\243F\374G\"%\242\271\225$\254\304g\201I\027\022\343\233j!O"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.16.0"
  }
  digests {
    sha256: "\242\'\366U\234\020J\245\245\310\213\np\351\307\343\333hY\343\vGt\363\202\221!\337\236bst"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.16.0"
  }
  digests {
    sha256: "\305\2369\261\263\033MU\222\'|\331\000\022\024\235t\vV\235C8\365dC\312\310,?\275\243\321"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.facebook.shimmer"
    artifactId: "shimmer"
    version: "0.5.0"
  }
  digests {
    sha256: "\323\313\031\323[[P\220\262>\240\a\323(\034K\245Ad\347\002\027T\006\371\323\2276\220\020K\352"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.airbnb.android"
    artifactId: "lottie"
    version: "6.6.6"
  }
  digests {
    sha256: "\341\244\363\302\buG;\226\252t\241\201\0222\177\216\320\213,\r$!\261xPc\313\237\314|\277"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.json"
    artifactId: "json"
    version: "20250107"
  }
  digests {
    sha256: "\205\324\301\253\031-1\027\375\002\307\377\361\354\017\346:\336E\317V\336\367\376\225\016\360`\317\006\351\237"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.10.1"
  }
  digests {
    sha256: "\305\271xs\230~P\373\301\235\334\253\227\032j\017\020\200\207B:\214\353\321\\\'M\260\320~\"E"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime-ktx"
    version: "2.10.1"
  }
  digests {
    sha256: "|\226\221\260\v\004\252\222\354_\340>\361s\215\026\212\206\322\342R:\363\236\236\236Ga\271\227\204}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "30.1-android"
  }
  digests {
    sha256: "\352\vZb\267\aH.\356\\\363\005\310\363^\371\034\364\316\257\365\004\360\021\245\304\234A5_W\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-compat-qual"
    version: "2.5.5"
  }
  digests {
    sha256: "\021\3214\262E\351\312\314GE\024\322\326k[\206\030\370\003\232\024e\315\305[\274\v4\340\000\213z"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.java-websocket"
    artifactId: "Java-WebSocket"
    version: "1.6.0"
  }
  digests {
    sha256: "\352\342\222\023\344\361e\025c\234(\225r\000\360\021\263\226\177\377\312\332\031b\317\002U\322I\031\302/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.slf4j"
    artifactId: "slf4j-api"
    version: "2.0.13"
  }
  digests {
    sha256: "\347\302\244\216\205\025\272\037I\372c}W\264\342\365\220\263\365\275\227@z\306\231\303\252^\373\022\004\251"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 7
  library_dep_index: 6
}
library_dependencies {
  library_index: 5
  library_dep_index: 3
  library_dep_index: 6
}
library_dependencies {
  library_index: 6
  library_dep_index: 3
}
library_dependencies {
  library_index: 7
  library_dep_index: 3
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
  library_dep_index: 1
  library_dep_index: 56
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 43
  library_dep_index: 59
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 64
  library_dep_index: 23
  library_dep_index: 39
  library_dep_index: 53
  library_dep_index: 68
  library_dep_index: 44
  library_dep_index: 3
  library_dep_index: 56
}
library_dependencies {
  library_index: 9
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 23
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 53
  library_dep_index: 44
  library_dep_index: 36
  library_dep_index: 3
  library_dep_index: 55
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 54
  library_dep_index: 3
  library_dep_index: 43
}
library_dependencies {
  library_index: 14
  library_dep_index: 3
}
library_dependencies {
  library_index: 15
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 17
  library_dep_index: 15
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 15
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 19
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 7
  library_dep_index: 5
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
  library_dep_index: 19
  library_dep_index: 18
}
library_dependencies {
  library_index: 21
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 5
}
library_dependencies {
  library_index: 22
  library_dep_index: 1
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 1
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 53
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 30
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 50
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 41
  library_dep_index: 3
}
library_dependencies {
  library_index: 25
  library_dep_index: 1
}
library_dependencies {
  library_index: 26
  library_dep_index: 1
  library_dep_index: 25
}
library_dependencies {
  library_index: 27
  library_dep_index: 3
  library_dep_index: 3
}
library_dependencies {
  library_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 29
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 23
  library_dep_index: 50
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 41
  library_dep_index: 3
}
library_dependencies {
  library_index: 31
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 28
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 23
  library_dep_index: 39
  library_dep_index: 3
  library_dep_index: 41
  library_dep_index: 38
  library_dep_index: 34
  library_dep_index: 52
  library_dep_index: 50
}
library_dependencies {
  library_index: 32
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 3
  library_dep_index: 30
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 23
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 52
  library_dep_index: 50
}
library_dependencies {
  library_index: 33
  library_dep_index: 32
  library_dep_index: 3
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 23
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 52
  library_dep_index: 50
}
library_dependencies {
  library_index: 34
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 35
  library_dep_index: 3
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 23
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 50
}
library_dependencies {
  library_index: 35
  library_dep_index: 1
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 1
  library_dep_index: 37
}
library_dependencies {
  library_index: 37
  library_dep_index: 36
  library_dep_index: 3
  library_dep_index: 36
}
library_dependencies {
  library_index: 38
  library_dep_index: 23
  library_dep_index: 3
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 23
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 50
}
library_dependencies {
  library_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 18
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 23
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 52
  library_dep_index: 50
}
library_dependencies {
  library_index: 41
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 1
  library_dep_index: 43
  library_dep_index: 32
  library_dep_index: 39
  library_dep_index: 44
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 18
  library_dep_index: 46
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 23
  library_dep_index: 50
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 3
}
library_dependencies {
  library_index: 43
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 3
  library_dep_index: 13
}
library_dependencies {
  library_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 1
  library_dep_index: 1
  library_dep_index: 43
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 46
  library_dep_index: 49
  library_dep_index: 3
}
library_dependencies {
  library_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
  library_dep_index: 3
}
library_dependencies {
  library_index: 48
  library_dep_index: 47
  library_dep_index: 46
}
library_dependencies {
  library_index: 49
  library_dep_index: 44
  library_dep_index: 3
  library_dep_index: 44
  library_dep_index: 3
}
library_dependencies {
  library_index: 50
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 23
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 41
  library_dep_index: 3
}
library_dependencies {
  library_index: 52
  library_dep_index: 39
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 23
  library_dep_index: 50
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 3
}
library_dependencies {
  library_index: 53
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 35
  library_dep_index: 16
}
library_dependencies {
  library_index: 54
  library_dep_index: 1
  library_dep_index: 10
}
library_dependencies {
  library_index: 55
  library_dep_index: 9
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 49
  library_dep_index: 3
  library_dep_index: 9
}
library_dependencies {
  library_index: 56
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 57
  library_dep_index: 58
  library_dep_index: 8
}
library_dependencies {
  library_index: 57
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 10
}
library_dependencies {
  library_index: 58
  library_dep_index: 57
  library_dep_index: 22
  library_dep_index: 10
}
library_dependencies {
  library_index: 59
  library_dep_index: 1
}
library_dependencies {
  library_index: 60
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 10
}
library_dependencies {
  library_index: 62
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 63
}
library_dependencies {
  library_index: 63
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 62
  library_dep_index: 62
}
library_dependencies {
  library_index: 64
  library_dep_index: 9
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 43
  library_dep_index: 32
  library_dep_index: 23
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 65
  library_dep_index: 53
  library_dep_index: 44
  library_dep_index: 66
  library_dep_index: 3
  library_dep_index: 67
}
library_dependencies {
  library_index: 65
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 31
  library_dep_index: 39
}
library_dependencies {
  library_index: 66
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 61
}
library_dependencies {
  library_index: 67
  library_dep_index: 55
  library_dep_index: 12
  library_dep_index: 43
  library_dep_index: 64
  library_dep_index: 33
  library_dep_index: 52
  library_dep_index: 49
  library_dep_index: 3
  library_dep_index: 64
}
library_dependencies {
  library_index: 68
  library_dep_index: 1
}
library_dependencies {
  library_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 9
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 13
  library_dep_index: 60
  library_dep_index: 76
  library_dep_index: 14
  library_dep_index: 64
  library_dep_index: 23
  library_dep_index: 81
  library_dep_index: 68
  library_dep_index: 84
  library_dep_index: 57
  library_dep_index: 83
}
library_dependencies {
  library_index: 70
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 7
  library_dep_index: 6
}
library_dependencies {
  library_index: 72
  library_dep_index: 1
}
library_dependencies {
  library_index: 73
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 61
  library_dep_index: 10
}
library_dependencies {
  library_index: 74
  library_dep_index: 8
  library_dep_index: 75
  library_dep_index: 13
  library_dep_index: 53
}
library_dependencies {
  library_index: 75
  library_dep_index: 1
}
library_dependencies {
  library_index: 76
  library_dep_index: 13
  library_dep_index: 10
  library_dep_index: 77
}
library_dependencies {
  library_index: 77
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 78
  library_dep_index: 65
  library_dep_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 78
  library_dep_index: 1
}
library_dependencies {
  library_index: 79
  library_dep_index: 1
}
library_dependencies {
  library_index: 80
  library_dep_index: 1
}
library_dependencies {
  library_index: 81
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 61
  library_dep_index: 82
  library_dep_index: 53
  library_dep_index: 83
}
library_dependencies {
  library_index: 82
  library_dep_index: 43
  library_dep_index: 3
}
library_dependencies {
  library_index: 83
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 64
  library_dep_index: 81
}
library_dependencies {
  library_index: 84
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 76
}
library_dependencies {
  library_index: 85
  library_dep_index: 9
  library_dep_index: 43
  library_dep_index: 67
  library_dep_index: 28
  library_dep_index: 32
  library_dep_index: 39
  library_dep_index: 86
  library_dep_index: 88
  library_dep_index: 44
  library_dep_index: 91
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 46
  library_dep_index: 86
  library_dep_index: 88
  library_dep_index: 90
  library_dep_index: 3
}
library_dependencies {
  library_index: 86
  library_dep_index: 87
}
library_dependencies {
  library_index: 87
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 43
  library_dep_index: 28
  library_dep_index: 23
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 53
  library_dep_index: 44
  library_dep_index: 49
  library_dep_index: 3
  library_dep_index: 46
  library_dep_index: 85
  library_dep_index: 88
  library_dep_index: 90
  library_dep_index: 3
}
library_dependencies {
  library_index: 88
  library_dep_index: 89
}
library_dependencies {
  library_index: 89
  library_dep_index: 55
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 43
  library_dep_index: 28
  library_dep_index: 23
  library_dep_index: 50
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 86
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 46
  library_dep_index: 86
  library_dep_index: 85
  library_dep_index: 90
  library_dep_index: 3
}
library_dependencies {
  library_index: 90
  library_dep_index: 8
  library_dep_index: 73
  library_dep_index: 43
  library_dep_index: 61
  library_dep_index: 60
  library_dep_index: 86
  library_dep_index: 88
  library_dep_index: 84
  library_dep_index: 69
  library_dep_index: 86
  library_dep_index: 85
  library_dep_index: 88
}
library_dependencies {
  library_index: 91
  library_dep_index: 1
  library_dep_index: 61
  library_dep_index: 13
  library_dep_index: 92
  library_dep_index: 84
}
library_dependencies {
  library_index: 92
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 13
}
library_dependencies {
  library_index: 93
  library_dep_index: 94
  library_dep_index: 3
}
library_dependencies {
  library_index: 94
  library_dep_index: 95
  library_dep_index: 5
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
}
library_dependencies {
  library_index: 96
  library_dep_index: 5
  library_dep_index: 7
}
library_dependencies {
  library_index: 97
  library_dep_index: 93
  library_dep_index: 98
}
library_dependencies {
  library_index: 98
  library_dep_index: 71
}
library_dependencies {
  library_index: 99
  library_dep_index: 94
  library_dep_index: 5
}
library_dependencies {
  library_index: 100
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 22
}
library_dependencies {
  library_index: 101
  library_dep_index: 102
}
library_dependencies {
  library_index: 102
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 10
  library_dep_index: 103
  library_dep_index: 106
  library_dep_index: 108
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 103
  library_dep_index: 105
  library_dep_index: 3
}
library_dependencies {
  library_index: 103
  library_dep_index: 104
}
library_dependencies {
  library_index: 104
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 105
  library_dep_index: 101
  library_dep_index: 3
}
library_dependencies {
  library_index: 105
  library_dep_index: 103
  library_dep_index: 101
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 103
  library_dep_index: 101
  library_dep_index: 3
}
library_dependencies {
  library_index: 106
  library_dep_index: 107
}
library_dependencies {
  library_index: 107
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 108
  library_dep_index: 3
}
library_dependencies {
  library_index: 108
  library_dep_index: 109
}
library_dependencies {
  library_index: 109
  library_dep_index: 1
  library_dep_index: 106
  library_dep_index: 3
  library_dep_index: 106
  library_dep_index: 3
}
library_dependencies {
  library_index: 110
  library_dep_index: 111
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 64
  library_dep_index: 58
  library_dep_index: 114
  library_dep_index: 36
}
library_dependencies {
  library_index: 111
  library_dep_index: 1
}
library_dependencies {
  library_index: 114
  library_dep_index: 1
}
library_dependencies {
  library_index: 115
  library_dep_index: 1
}
library_dependencies {
  library_index: 116
  library_dep_index: 95
  library_dep_index: 8
}
library_dependencies {
  library_index: 118
  library_dep_index: 14
  library_dep_index: 17
  library_dep_index: 13
  library_dep_index: 31
  library_dep_index: 38
  library_dep_index: 105
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 16
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 119
  library_dep_index: 3
}
library_dependencies {
  library_index: 119
  library_dep_index: 118
  library_dep_index: 118
}
library_dependencies {
  library_index: 120
  library_dep_index: 121
  library_dep_index: 16
  library_dep_index: 122
  library_dep_index: 123
  library_dep_index: 71
  library_dep_index: 124
}
library_dependencies {
  library_index: 125
  library_dep_index: 126
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 8
  dependency_index: 69
  dependency_index: 74
  dependency_index: 85
  dependency_index: 90
  dependency_index: 39
  dependency_index: 31
  dependency_index: 93
  dependency_index: 97
  dependency_index: 94
  dependency_index: 99
  dependency_index: 98
  dependency_index: 100
  dependency_index: 101
  dependency_index: 64
  dependency_index: 81
  dependency_index: 110
  dependency_index: 115
  dependency_index: 116
  dependency_index: 117
  dependency_index: 105
  dependency_index: 118
  dependency_index: 119
  dependency_index: 15
  dependency_index: 120
  dependency_index: 125
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
