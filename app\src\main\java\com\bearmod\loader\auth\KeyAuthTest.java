package com.bearmod.loader.auth;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.bearmod.loader.BearLoaderApplication;

/**
 * Test class for KeyAuth API connection
 */
public class KeyAuthTest {
    private static final String TAG = "KeyAuthTest";

    /**
     * Test KeyAuth connection
     * @param context Application context
     * @param callback Callback for test result
     */
    public static void testConnection(Context context, TestCallback callback) {
        // Create handler for main thread callbacks
        Handler handler = new Handler(Looper.getMainLooper());

        // Check if in development mode
        if (BearLoaderApplication.getInstance().isDevelopmentMode()) {
            // In development mode, return success with development mode message
            handler.postDelayed(() -> {
                callback.onResult(true, "Development Mode: KeyAuth API connection test skipped. Using mock data.");
            }, 500);
            return;
        }

        // Run test in background thread
        new Thread(() -> {
            try {
                // Get KeyAuth manager instance
                DirectKeyAuthManager keyAuthManager = DirectKeyAuthManager.getInstance();

                // Initialize KeyAuth
                boolean initSuccess = keyAuthManager.initialize(context);

                if (!initSuccess) {
                    handler.post(() -> callback.onResult(false, "Failed to initialize KeyAuth. Check internet connection and API domain."));
                    return;
                }

                // Test license key from previous conversation
                String testKey = "4PjqHA-QtCw6Q-jyrU9S-0kPxSX-ZO2G3k";

                // Try to login with test key
                keyAuthManager.login(testKey, new DirectKeyAuthManager.AuthCallback() {
                    @Override
                    public void onSuccess(AuthResult result) {
                        Log.d(TAG, "KeyAuth test successful: " + result.getMessage());
                        // Check which URL was used
                        String usedUrl;
                        if (keyAuthManager.isUsingCustomUrl()) {
                            usedUrl = keyAuthManager.getCustomDomain();
                        } else if (keyAuthManager.isUsingAlternateUrl()) {
                            usedUrl = "prod.keyauth.com";
                        } else {
                            usedUrl = "keyauth.win";
                        }
                        callback.onResult(true, "KeyAuth API connection successful! Domain '" + usedUrl + "' is working.");
                    }

                    @Override
                    public void onError(String error) {
                        // Even if login fails with invalid key, if we get a proper response from the server,
                        // it means the connection is working
                        if (error.contains("invalid") || error.contains("Invalid")) {
                            Log.d(TAG, "KeyAuth test successful (invalid key response): " + error);
                            // Check which URL was used
                            String usedUrl;
                            if (keyAuthManager.isUsingCustomUrl()) {
                                usedUrl = keyAuthManager.getCustomDomain();
                            } else if (keyAuthManager.isUsingAlternateUrl()) {
                                usedUrl = "prod.keyauth.com";
                            } else {
                                usedUrl = "keyauth.win";
                            }
                            callback.onResult(true, "KeyAuth API connection successful! Domain '" + usedUrl + "' is working. (Key validation failed: " + error + ")");
                        } else {
                            Log.e(TAG, "KeyAuth test failed: " + error);
                            callback.onResult(false, "KeyAuth API connection failed: " + error);
                        }
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "KeyAuth test exception: " + e.getMessage());
                handler.post(() -> callback.onResult(false, "KeyAuth test exception: " + e.getMessage()));
            }
        }).start();
    }

    /**
     * Callback for test result
     */
    public interface TestCallback {
        void onResult(boolean success, String message);
    }
}
