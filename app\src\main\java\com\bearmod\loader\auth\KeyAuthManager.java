package com.bearmod.loader.auth;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.bearmod.loader.BearLoaderApplication;
import com.keyauth.api.KeyAuth;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * KeyAuth authentication manager
 * Handles user authentication using KeyAuth
 */
public class KeyAuthManager {

    private static final String TAG = "KeyAuthManager";

    private static KeyAuthManager instance;
    private final Executor executor = Executors.newSingleThreadExecutor();
    private final Handler handler = new Handler(Looper.getMainLooper());

    // KeyAuth configuration
    private static final String APP_NAME = "com.bearmod.loader";
    private static final String APP_OWNER = "yLoA9zcOEF";
    private static final String APP_VERSION = "1.0";

    // KeyAuth instance
    private KeyAuth keyAuth;
    private com.keyauth.api.KeyAuthApp keyAuthApp;
    private boolean isInitialized = false;

    private KeyAuthManager() {
        // Private constructor to enforce singleton pattern
    }

    /**
     * Get KeyAuthManager instance
     * @return KeyAuthManager instance
     */
    public static synchronized KeyAuthManager getInstance() {
        if (instance == null) {
            instance = new KeyAuthManager();
        }
        return instance;
    }

    /**
     * Initialize KeyAuth
     * @param context Application context
     */
    public void initialize(Context context) {
        try {
            // Initialize KeyAuth SDK
            keyAuth = new KeyAuth();
            keyAuthApp = new com.keyauth.api.KeyAuthApp(APP_NAME, APP_OWNER, APP_VERSION);

            // Initialize the API
            boolean initSuccess = keyAuthApp.init();

            // Check if initialization was successful
            if (initSuccess) {
                isInitialized = true;
                Log.d(TAG, "KeyAuth initialized successfully");
            } else {
                Log.e(TAG, "KeyAuth initialization failed: " + keyAuthApp.getResponse());
            }
        } catch (Exception e) {
            Log.e(TAG, "Error initializing KeyAuth: " + e.getMessage());
        }
    }

    /**
     * Login with license key
     * @param licenseKey License key
     * @param callback Callback for login result
     */
    public void login(String licenseKey, AuthCallback callback) {
        // Check if KeyAuth is initialized
        if (!isInitialized) {
            handler.post(() -> callback.onError("KeyAuth not initialized"));
            return;
        }

        // Execute login in background thread
        executor.execute(() -> {
            try {
                // Attempt to login with license key
                boolean success = keyAuthApp.license(licenseKey);

                if (success) {
                    // Get license information
                    String expiryString = keyAuthApp.getExpiryDate();
                    String registrationDate = keyAuthApp.getRegistrationDate();

                    // Parse expiry date
                    Date expiryDate;
                    try {
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
                        expiryDate = dateFormat.parse(expiryString);
                    } catch (Exception e) {
                        // Use a default expiry date if parsing fails
                        expiryDate = new Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000); // 30 days
                    }

                    // Save license key and login status
                    BearLoaderApplication.getInstance().saveLicenseKey(licenseKey);
                    BearLoaderApplication.getInstance().setLoggedIn(true);

                    // Create auth result
                    final AuthResult result = new AuthResult(
                            true,
                            "Login successful",
                            expiryDate,
                            registrationDate
                    );

                    // Return success on main thread
                    handler.post(() -> callback.onSuccess(result));
                } else {
                    // Return error on main thread
                    String errorMessage = keyAuthApp.getResponse();
                    handler.post(() -> callback.onError(errorMessage));
                }
            } catch (Exception e) {
                Log.e(TAG, "Login error: " + e.getMessage());
                // Return error on main thread
                handler.post(() -> callback.onError("Login failed: " + e.getMessage()));
            }
        });
    }

    /**
     * Check if license is valid
     * @param callback Callback for validation result
     */
    public void validateLicense(AuthCallback callback) {
        // Check if KeyAuth is initialized
        if (!isInitialized) {
            handler.post(() -> callback.onError("KeyAuth not initialized"));
            return;
        }

        // Get license key
        String licenseKey = BearLoaderApplication.getInstance().getLicenseKey();

        // Check if license key exists
        if (licenseKey == null || licenseKey.isEmpty()) {
            handler.post(() -> callback.onError("No license key found"));
            return;
        }

        // Execute validation in background thread
        executor.execute(() -> {
            try {
                // Attempt to validate license
                boolean success = keyAuthApp.license(licenseKey);

                if (success) {
                    // Get license information
                    String expiryString = keyAuthApp.getExpiryDate();
                    String registrationDate = keyAuthApp.getRegistrationDate();

                    // Parse expiry date
                    Date expiryDate;
                    try {
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
                        expiryDate = dateFormat.parse(expiryString);
                    } catch (Exception e) {
                        // Use a default expiry date if parsing fails
                        expiryDate = new Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000); // 30 days
                    }

                    // Create auth result
                    final AuthResult result = new AuthResult(
                            true,
                            "License valid",
                            expiryDate,
                            registrationDate
                    );

                    // Return success on main thread
                    handler.post(() -> callback.onSuccess(result));
                } else {
                    // Return error on main thread
                    String errorMessage = keyAuthApp.getResponse();
                    handler.post(() -> callback.onError(errorMessage));

                    // Clear login status
                    BearLoaderApplication.getInstance().setLoggedIn(false);
                }
            } catch (Exception e) {
                Log.e(TAG, "License validation error: " + e.getMessage());
                // Return error on main thread
                handler.post(() -> callback.onError("Validation failed: " + e.getMessage()));

                // Clear login status
                BearLoaderApplication.getInstance().setLoggedIn(false);
            }
        });
    }

    /**
     * Logout user
     */
    public void logout() {
        try {
            // Logout from KeyAuth if initialized
            if (isInitialized && keyAuthApp != null) {
                keyAuthApp.logout();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error during logout: " + e.getMessage());
        } finally {
            // Clear user data regardless of KeyAuth logout success
            BearLoaderApplication.getInstance().clearUserData();
        }
    }

    /**
     * Format expiry date
     * @param date Expiry date
     * @return Formatted date string
     */
    public String formatExpiryDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        return sdf.format(date);
    }

    /**
     * Get remaining days until expiry
     * @param expiryDate Expiry date
     * @return Remaining days
     */
    public int getRemainingDays(Date expiryDate) {
        long diff = expiryDate.getTime() - System.currentTimeMillis();
        return (int) (diff / (24 * 60 * 60 * 1000));
    }

    /**
     * Auth callback interface
     */
    public interface AuthCallback {
        void onSuccess(AuthResult result);
        void onError(String error);
    }
}
