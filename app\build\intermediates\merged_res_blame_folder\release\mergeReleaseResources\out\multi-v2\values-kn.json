{"logs": [{"outputFile": "com.bearmod.loader.app-mergeReleaseResources-55:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a95a1075bc0403a1ca2712bac24841aa\\transformed\\material-1.12.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,277,361,444,526,641,736,843,956,1041,1098,1161,1255,1321,1383,1486,1552,1623,1682,1758,1823,1877,1990,2048,2109,2163,2242,2358,2444,2527,2622,2708,2799,2941,3020,3099,3228,3316,3400,3457,3509,3575,3655,3745,3816,3895,3972,4049,4126,4195,4312,4411,4488,4581,4676,4750,4831,4927,4978,5062,5130,5216,5304,5367,5432,5495,5563,5668,5773,5868,5971,6032,6088,6170,6262,6341", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,82,81,114,94,106,112,84,56,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,85,82,94,85,90,141,78,78,128,87,83,56,51,65,79,89,70,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81,91,78,73", "endOffsets": "272,356,439,521,636,731,838,951,1036,1093,1156,1250,1316,1378,1481,1547,1618,1677,1753,1818,1872,1985,2043,2104,2158,2237,2353,2439,2522,2617,2703,2794,2936,3015,3094,3223,3311,3395,3452,3504,3570,3650,3740,3811,3890,3967,4044,4121,4190,4307,4406,4483,4576,4671,4745,4826,4922,4973,5057,5125,5211,5299,5362,5427,5490,5558,5663,5768,5863,5966,6027,6083,6165,6257,6336,6410"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3078,3162,3245,3327,3442,4282,4389,4502,4587,4644,4707,4801,4867,4929,5032,5098,5169,5228,5304,5369,5423,5536,5594,5655,5709,5788,5904,5990,6073,6168,6254,6345,6487,6566,6645,6774,6862,6946,7003,7055,7121,7201,7291,7362,7441,7518,7595,7672,7741,7858,7957,8034,8127,8222,8296,8377,8473,8524,8608,8676,8762,8850,8913,8978,9041,9109,9214,9319,9414,9517,9578,9881,10046,10138,10217", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "endColumns": "12,83,82,81,114,94,106,112,84,56,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,85,82,94,85,90,141,78,78,128,87,83,56,51,65,79,89,70,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81,91,78,73", "endOffsets": "322,3157,3240,3322,3437,3532,4384,4497,4582,4639,4702,4796,4862,4924,5027,5093,5164,5223,5299,5364,5418,5531,5589,5650,5704,5783,5899,5985,6068,6163,6249,6340,6482,6561,6640,6769,6857,6941,6998,7050,7116,7196,7286,7357,7436,7513,7590,7667,7736,7853,7952,8029,8122,8217,8291,8372,8468,8519,8603,8671,8757,8845,8908,8973,9036,9104,9209,9314,9409,9512,9573,9629,9958,10133,10212,10286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b19140129864dc831a67762e70345bb\\transformed\\navigation-ui-2.9.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,173", "endColumns": "117,128", "endOffsets": "168,297"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9634,9752", "endColumns": "117,128", "endOffsets": "9747,9876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\84e22dff9d3244eb5f30d696cec58c96\\transformed\\appcompat-1.7.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,529,636,762,840,916,1007,1100,1195,1289,1389,1482,1577,1671,1762,1853,1935,2051,2161,2260,2373,2478,2592,2756,2856", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,524,631,757,835,911,1002,1095,1190,1284,1384,1477,1572,1666,1757,1848,1930,2046,2156,2255,2368,2473,2587,2751,2851,2934"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "327,441,553,666,751,858,984,1062,1138,1229,1322,1417,1511,1611,1704,1799,1893,1984,2075,2157,2273,2383,2482,2595,2700,2814,2978,9963", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "436,548,661,746,853,979,1057,1133,1224,1317,1412,1506,1606,1699,1794,1888,1979,2070,2152,2268,2378,2477,2590,2695,2809,2973,3073,10041"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95f2be59c03f86888596a581fb73e161\\transformed\\core-1.13.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "38,39,40,41,42,43,44,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3537,3635,3738,3839,3945,4046,4154,10291", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3630,3733,3834,3940,4041,4149,4277,10387"}}]}]}